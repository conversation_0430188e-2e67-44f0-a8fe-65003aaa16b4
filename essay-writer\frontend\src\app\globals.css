@import "@copilotkit/react-ui/styles.css";
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义样式 */
.essay-content {
  @apply prose prose-lg max-w-none;
  line-height: 1.8;
}

.essay-content h1,
.essay-content h2,
.essay-content h3 {
  @apply text-foreground font-bold;
}

.essay-content p {
  @apply text-foreground mb-4;
  text-indent: 2em;
}

.progress-bar {
  @apply w-full bg-secondary rounded-full h-2;
}

.progress-fill {
  @apply bg-primary h-2 rounded-full transition-all duration-300 ease-out;
}

.confirmation-timer {
  @apply inline-flex items-center gap-2 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium;
}

.evaluation-score {
  @apply text-2xl font-bold text-primary;
}

.evaluation-criteria {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.criteria-item {
  @apply flex justify-between items-center p-3 bg-card rounded-lg border;
}

.streaming-text {
  @apply animate-pulse;
}

.message-bubble {
  @apply max-w-3xl mx-auto p-4 rounded-lg;
}

.user-message {
  @apply bg-primary text-primary-foreground ml-auto;
}

.assistant-message {
  @apply bg-card text-card-foreground;
}

.status-indicator {
  @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium;
}

.status-generating {
  @apply bg-blue-100 text-blue-800;
}

.status-waiting {
  @apply bg-yellow-100 text-yellow-800;
}

.status-revising {
  @apply bg-orange-100 text-orange-800;
}

.status-evaluating {
  @apply bg-purple-100 text-purple-800;
}

.status-completed {
  @apply bg-green-100 text-green-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

/* 动画效果 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter-effect {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typewriter 2s steps(40, end);
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.cursor-blink {
  animation: blink 1s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .essay-content {
    @apply prose-sm;
  }
  
  .evaluation-criteria {
    @apply grid-cols-1;
  }
  
  .message-bubble {
    @apply mx-4;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-foreground;
}
