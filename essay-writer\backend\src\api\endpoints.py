"""
AG-UI协议端点实现
"""
import asyncio
import uuid
import logging
from datetime import datetime
from typing import AsyncGenerator
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from ..models.schemas import (
    RunAgentInput, EssayState, EventType,
    RunStartedEvent, RunFinishedEvent, StateSnapshotEvent, StateDeltaEvent,
    TextMessageStartEvent, TextMessageContentEvent, TextMessageEndEvent
)
from ..utils.event_encoder import EventEncoder
from ..agents.essay_generator import EssayGeneratorAgent
from ..agents.essay_evaluator import EssayEvaluatorAgent
from ..utils.prompts import CONFIRMATION_PROMPT, AUTO_CONFIRMATION_MESSAGE

logger = logging.getLogger(__name__)

router = APIRouter()

# 全局状态存储（生产环境中应使用Redis等持久化存储）
workflow_states = {}


@router.post("/essay-writer")
async def essay_writer_endpoint(input_data: RunAgentInput):
    """
    AG-UI协议主端点 - 作文生成和评分
    
    支持的功能：
    1. 根据用户输入生成作文
    2. 流式输出作文内容
    3. 等待用户确认（10秒自动确认）
    4. 根据用户反馈修改作文
    5. AI评分和建议
    """
    
    async def event_generator() -> AsyncGenerator[str, None]:
        encoder = EventEncoder()
        message_id = str(uuid.uuid4())
        
        try:
            # 1. 发送运行开始事件
            yield encoder.encode(RunStartedEvent(
                thread_id=input_data.thread_id,
                run_id=input_data.run_id
            ))
            
            # 2. 初始化代理
            generator = EssayGeneratorAgent()
            evaluator = EssayEvaluatorAgent()
            
            # 3. 解析用户输入
            user_message = ""
            for msg in input_data.messages:
                if msg.role == "user":
                    user_message = msg.content
                    break
            
            if not user_message:
                raise ValueError("没有找到用户输入")
            
            # 检查是否是修改请求
            current_state = workflow_states.get(input_data.thread_id, {})
            is_revision = current_state.get("waiting_for_feedback", False)
            
            if is_revision and user_message.lower() not in ["确认", "继续", "confirm"]:
                # 处理修改请求
                yield from handle_revision(
                    encoder, message_id, generator, evaluator,
                    current_state, user_message, input_data
                )
            else:
                # 处理新的作文生成请求或确认
                if is_revision and user_message.lower() in ["确认", "继续", "confirm"]:
                    # 用户确认，进行评分
                    yield from handle_evaluation(
                        encoder, message_id, evaluator,
                        current_state, input_data
                    )
                else:
                    # 新的作文生成请求
                    yield from handle_new_essay(
                        encoder, message_id, generator,
                        user_message, input_data
                    )
            
            # 发送运行结束事件
            yield encoder.encode(RunFinishedEvent(
                thread_id=input_data.thread_id,
                run_id=input_data.run_id
            ))
            
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            
            # 发送错误状态
            yield encoder.encode_status_update(
                message_id=message_id,
                phase="error",
                error=str(e)
            )
            
            # 发送错误消息
            yield encoder.encode(TextMessageStartEvent(
                message_id=message_id,
                role="assistant"
            ))
            
            yield encoder.encode(TextMessageContentEvent(
                message_id=message_id,
                delta=f"抱歉，处理您的请求时出现错误：{str(e)}"
            ))
            
            yield encoder.encode(TextMessageEndEvent(
                message_id=message_id
            ))
            
            # 发送运行结束事件
            yield encoder.encode(RunFinishedEvent(
                thread_id=input_data.thread_id,
                run_id=input_data.run_id
            ))
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"  # 禁用Nginx缓冲
        }
    )


async def handle_new_essay(
    encoder: EventEncoder,
    message_id: str,
    generator: EssayGeneratorAgent,
    user_message: str,
    input_data: RunAgentInput
) -> AsyncGenerator[str, None]:
    """处理新的作文生成请求"""
    
    # 解析主题和要求
    parsed = generator.extract_topic_and_requirements(user_message)
    topic = parsed["topic"]
    requirements = parsed["requirements"]
    
    # 发送初始状态快照
    initial_state = EssayState()
    initial_state.essay["topic"] = topic
    initial_state.essay["requirements"] = requirements
    initial_state.status["phase"] = "generating"
    initial_state.processing["in_progress"] = True
    
    yield encoder.encode(StateSnapshotEvent(
        message_id=message_id,
        snapshot=initial_state.dict()
    ))
    
    # 发送进度更新
    yield encoder.encode_progress_update(
        message_id=message_id,
        progress=0.1,
        step="开始生成作文..."
    )
    
    # 开始生成作文
    yield encoder.encode(TextMessageStartEvent(
        message_id=message_id,
        role="assistant"
    ))
    
    essay_content = ""
    async for chunk in generator.generate_essay(topic, requirements):
        essay_content += chunk
        yield encoder.encode(TextMessageContentEvent(
            message_id=message_id,
            delta=chunk
        ))
        
        # 更新进度
        progress = min(0.8, len(essay_content) / 1000)  # 假设1000字为满进度
        yield encoder.encode_progress_update(
            message_id=message_id,
            progress=progress,
            step="正在生成作文内容..."
        )
    
    yield encoder.encode(TextMessageEndEvent(message_id=message_id))
    
    # 更新状态 - 等待确认
    yield encoder.encode_status_update(
        message_id=message_id,
        phase="waiting_confirmation"
    )
    
    yield encoder.encode_progress_update(
        message_id=message_id,
        progress=0.9,
        step="作文生成完成，等待确认..."
    )
    
    # 保存状态
    workflow_states[input_data.thread_id] = {
        "essay_content": essay_content,
        "topic": topic,
        "requirements": requirements,
        "waiting_for_feedback": True,
        "revision_count": 0
    }
    
    # 发送确认提示
    yield encoder.encode(TextMessageStartEvent(
        message_id=str(uuid.uuid4()),
        role="assistant"
    ))
    
    yield encoder.encode(TextMessageContentEvent(
        message_id=message_id,
        delta=CONFIRMATION_PROMPT
    ))
    
    yield encoder.encode(TextMessageEndEvent(message_id=message_id))


async def handle_revision(
    encoder: EventEncoder,
    message_id: str,
    generator: EssayGeneratorAgent,
    evaluator: EssayEvaluatorAgent,
    current_state: dict,
    feedback: str,
    input_data: RunAgentInput
) -> AsyncGenerator[str, None]:
    """处理作文修改请求"""
    
    revision_count = current_state.get("revision_count", 0)
    max_revisions = 3
    
    if revision_count >= max_revisions:
        # 达到最大修改次数，直接评分
        yield from handle_evaluation(
            encoder, message_id, evaluator, current_state, input_data
        )
        return
    
    # 发送修改状态
    yield encoder.encode_status_update(
        message_id=message_id,
        phase="revising"
    )
    
    yield encoder.encode_progress_update(
        message_id=message_id,
        progress=0.2,
        step=f"正在根据您的意见修改作文（第{revision_count + 1}次修改）..."
    )
    
    # 开始修改作文
    yield encoder.encode(TextMessageStartEvent(
        message_id=message_id,
        role="assistant"
    ))
    
    revised_content = ""
    async for chunk in generator.revise_essay(
        current_state["essay_content"],
        feedback,
        current_state["topic"]
    ):
        revised_content += chunk
        yield encoder.encode(TextMessageContentEvent(
            message_id=message_id,
            delta=chunk
        ))
        
        # 更新进度
        progress = 0.2 + min(0.6, len(revised_content) / 1000)
        yield encoder.encode_progress_update(
            message_id=message_id,
            progress=progress,
            step="正在修改作文内容..."
        )
    
    yield encoder.encode(TextMessageEndEvent(message_id=message_id))
    
    # 更新状态
    current_state["essay_content"] = revised_content
    current_state["revision_count"] = revision_count + 1
    workflow_states[input_data.thread_id] = current_state
    
    # 发送确认提示
    yield encoder.encode_status_update(
        message_id=message_id,
        phase="waiting_confirmation"
    )
    
    yield encoder.encode(TextMessageStartEvent(
        message_id=str(uuid.uuid4()),
        role="assistant"
    ))
    
    yield encoder.encode(TextMessageContentEvent(
        message_id=message_id,
        delta=CONFIRMATION_PROMPT
    ))
    
    yield encoder.encode(TextMessageEndEvent(message_id=message_id))


async def handle_evaluation(
    encoder: EventEncoder,
    message_id: str,
    evaluator: EssayEvaluatorAgent,
    current_state: dict,
    input_data: RunAgentInput
) -> AsyncGenerator[str, None]:
    """处理作文评分"""
    
    # 发送评分状态
    yield encoder.encode_status_update(
        message_id=message_id,
        phase="evaluating"
    )
    
    yield encoder.encode_progress_update(
        message_id=message_id,
        progress=0.9,
        step="正在进行AI评分..."
    )
    
    # 开始评分
    yield encoder.encode(TextMessageStartEvent(
        message_id=message_id,
        role="assistant"
    ))
    
    evaluation_content = ""
    async for chunk in evaluator.evaluate_essay(
        current_state["essay_content"],
        current_state["topic"],
        current_state.get("requirements", "")
    ):
        evaluation_content += chunk
        yield encoder.encode(TextMessageContentEvent(
            message_id=message_id,
            delta=chunk
        ))
    
    yield encoder.encode(TextMessageEndEvent(message_id=message_id))
    
    # 完成状态
    yield encoder.encode_status_update(
        message_id=message_id,
        phase="completed"
    )
    
    yield encoder.encode_progress_update(
        message_id=message_id,
        progress=1.0,
        step="评分完成！"
    )
    
    # 清除状态
    if input_data.thread_id in workflow_states:
        del workflow_states[input_data.thread_id]
