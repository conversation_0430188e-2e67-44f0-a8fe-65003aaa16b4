/**
 * CopilotKit API路由 - 连接前端和AG-UI后端
 */
import { HttpAgent } from "@ag-ui/client";
import {
  CopilotRuntime,
  ExperimentalEmptyAdapter,
  copilotRuntimeNextJSAppRouterEndpoint,
} from "@copilotkit/runtime";
import { NextRequest } from "next/server";

// 创建HttpAgent连接到后端
const essayWriterAgent = new HttpAgent({
  url: process.env.NEXT_PUBLIC_API_BASE_URL 
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/essay-writer`
    : "http://localhost:8000/api/essay-writer",
});

// 初始化CopilotKit运行时
const runtime = new CopilotRuntime({
  agents: {
    essayWriterAgent,
  },
});

/**
 * POST请求处理器
 */
export const POST = async (req: NextRequest) => {
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter: new ExperimentalEmptyAdapter(),
    endpoint: "/api/copilotkit",
  });

  return handleRequest(req);
};
