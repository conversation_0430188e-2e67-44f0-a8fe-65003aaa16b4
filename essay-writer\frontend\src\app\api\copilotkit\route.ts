/**
 * CopilotKit API路由 - 代理到后端API
 */
import { NextRequest, NextResponse } from "next/server";

/**
 * POST请求处理器 - 直接代理到后端
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // 后端API URL
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/essay-writer`
      : "http://localhost:8000/api/essay-writer";

    // 代理请求到后端
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with ${response.status}`);
    }

    // 返回流式响应
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error('API proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
