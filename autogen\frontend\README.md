# ChatDB Frontend

This is the frontend for the ChatDB Text2SQL system. It's built with React, TypeScript, and Ant Design.

## Features

- Database connection management
- Interactive schema visualization and management
- Natural language query interface
- Value mapping management

## Getting Started

### Prerequisites

- Node.js v20.19.1
- npm or yarn

### Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server:
   ```
   npm start
   ```

3. Build for production:
   ```
   npm run build
   ```

## Project Structure

- `src/components/`: Reusable UI components
- `src/pages/`: Main application pages
- `src/services/`: API services for backend communication
- `src/utils/`: Utility functions
