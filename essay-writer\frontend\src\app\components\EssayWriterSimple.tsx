"use client";

import React, { useState, useRef } from 'react';
import { PenTool, Send, Loader2 } from 'lucide-react';
import { ESSAY_PHASES } from '../../types';
import ProgressBar from './ProgressBar';
import StatusIndicator from './StatusIndicator';
// import ConfirmationDialog from './ConfirmationDialog'; // 不再使用外部对话框
import EvaluationDisplay from './EvaluationDisplay';

export default function EssayWriter() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationTimer, setConfirmationTimer] = useState(10);
  const [userInput, setUserInput] = useState('');
  const [essayContent, setEssayContent] = useState('');
  const [currentPhase, setCurrentPhase] = useState<typeof ESSAY_PHASES[keyof typeof ESSAY_PHASES]>('idle');
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [evaluation, setEvaluation] = useState<any>(null);
  const [messages, setMessages] = useState<Array<{role: string, content: string, timestamp: string}>>([]);
  const [showRevisionInput, setShowRevisionInput] = useState(false);
  const [revisionText, setRevisionText] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 处理SSE流式响应
  const handleStreamResponse = async (response: Response) => {
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) return;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              handleSSEEvent(data);
            } catch (e) {
              console.error('Failed to parse SSE data:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('Stream reading error:', error);
      setIsProcessing(false);
      setCurrentPhase('error');
    } finally {
      reader.releaseLock();
    }
  };

  // 处理SSE事件
  const handleSSEEvent = (event: any) => {
    console.log('SSE Event:', event);
    console.log('Current Phase:', currentPhase);

    switch (event.type) {
      case 'RUN_STARTED':
        setIsProcessing(true);
        setCurrentPhase('generating');
        setProgress(0.1);
        setCurrentStep('开始生成作文...');
        setShowConfirmation(false);
        setShowRevisionInput(false);
        setIsConfirming(false);
        break;
      
      case 'STATE_DELTA':
        if (event.delta) {
          event.delta.forEach((delta: any) => {
            if (delta.path.includes('progress')) {
              setProgress(delta.value);
            }
            if (delta.path.includes('current_step')) {
              setCurrentStep(delta.value);
            }
            if (delta.path.includes('phase')) {
              setCurrentPhase(delta.value);
            }
          });
        }
        break;
      
      case 'TEXT_MESSAGE_START':
        setEssayContent('');
        break;
      
      case 'TEXT_MESSAGE_CONTENT':
        setEssayContent(prev => prev + event.delta);
        break;
      
      case 'TEXT_MESSAGE_END':
        console.log('TEXT_MESSAGE_END received, currentPhase:', currentPhase);
        setCurrentPhase('waiting_confirmation');
        setShowConfirmation(true);
        setIsProcessing(false);
        setProgress(1);
        setCurrentStep('作文生成完成，等待确认...');
        startConfirmationTimer();
        break;
      
      case 'RUN_FINISHED':
        setIsProcessing(false);
        break;
    }
  };

  // 启动确认计时器
  const startConfirmationTimer = () => {
    setConfirmationTimer(10);
    timerRef.current = setInterval(() => {
      setConfirmationTimer(prev => {
        if (prev <= 1) {
          handleConfirm(); // 自动确认
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 发送请求到后端
  const sendRequest = async (message: string) => {
    try {
      setIsProcessing(true);
      setCurrentPhase('generating');
      
      const requestBody = {
        thread_id: `thread_${Date.now()}`,
        run_id: `run_${Date.now()}`,
        messages: [
          {
            id: `msg_${Date.now()}`,
            role: 'user',
            content: message
          }
        ]
      };

      const response = await fetch('/api/copilotkit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      await handleStreamResponse(response);
    } catch (error) {
      console.error('Request failed:', error);
      setIsProcessing(false);
      setCurrentPhase('error');
      setCurrentStep('请求失败，请重试');
    }
  };

  // 处理用户输入提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim() || isProcessing) return;

    const message = userInput.trim();
    setUserInput('');
    
    // 添加用户消息到聊天记录
    setMessages(prev => [...prev, {
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    }]);

    await sendRequest(message);
  };

  // 处理确认
  const handleConfirm = () => {
    if (isConfirming) return; // 防止重复调用

    setIsConfirming(true);
    setShowConfirmation(false);
    setShowRevisionInput(false);
    setCurrentPhase('evaluating');
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // 添加确认消息到聊天记录
    setMessages(prev => [...prev, {
      role: 'user',
      content: '确认',
      timestamp: new Date().toISOString()
    }]);

    // 发送确认消息
    sendRequest('确认');
  };

  // 显示修改输入框
  const handleShowRevisionInput = () => {
    setShowRevisionInput(true);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  // 处理修改提交
  const handleRevisionSubmit = () => {
    if (!revisionText.trim()) return;

    setShowConfirmation(false);
    setShowRevisionInput(false);
    setCurrentPhase('revising');

    // 添加修改意见到聊天记录
    setMessages(prev => [...prev, {
      role: 'user',
      content: revisionText,
      timestamp: new Date().toISOString()
    }]);

    // 发送修改意见
    sendRequest(revisionText);
    setRevisionText('');
  };

  // 取消修改
  const handleCancelRevision = () => {
    setShowRevisionInput(false);
    setRevisionText('');
    // 重新启动倒计时
    startConfirmationTimer();
  };

  return (
    <div className="space-y-6">
      {/* 聊天消息区域 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          💬 作文生成对话
        </h2>
        
        {/* 消息列表 */}
        <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
          {messages.length === 0 && !isProcessing && (
            <div className="text-center text-gray-500 py-8">
              <PenTool className="w-12 h-12 mx-auto mb-2 text-gray-400" />
              <p>请输入您想写的作文主题开始生成</p>
            </div>
          )}
          
          {messages.map((message, index) => (
            <div
              key={index}
              className={`message-bubble ${
                message.role === 'user' ? 'user-message' : 'assistant-message'
              }`}
            >
              <div className="font-medium mb-1">
                {message.role === 'user' ? '您' : 'AI助手'}
              </div>
              <div className="whitespace-pre-wrap">{message.content}</div>
            </div>
          ))}
          
          {/* 实时生成的作文内容 */}
          {essayContent && (
            <div className="assistant-message">
              <div className="font-medium mb-1">AI助手</div>
              <div className="essay-content whitespace-pre-wrap">
                {essayContent}
                {isProcessing && currentPhase === 'generating' && (
                  <span className="cursor-blink">|</span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 处理状态显示 */}
        {isProcessing && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
              <StatusIndicator phase={currentPhase} step={currentStep} />
            </div>
            
            {progress > 0 && (
              <ProgressBar progress={progress} step={currentStep} />
            )}
          </div>
        )}

        {/* 调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-2 bg-gray-100 text-xs">
            <div>Phase: {currentPhase}</div>
            <div>Show Confirmation: {showConfirmation.toString()}</div>
            <div>Show Revision: {showRevisionInput.toString()}</div>
            <div>Is Processing: {isProcessing.toString()}</div>
            <div>Is Confirming: {isConfirming.toString()}</div>
          </div>
        )}

        {/* 确认控件 */}
        {showConfirmation && !showRevisionInput && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900">✅ 作文生成完成</h3>
              <div className="text-sm text-gray-600">
                自动确认倒计时: <span className="font-mono font-bold text-red-600">{confirmationTimer}s</span>
              </div>
            </div>
            <p className="text-gray-700 mb-4">请选择下一步操作：</p>
            <div className="flex gap-3">
              <button
                onClick={handleConfirm}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                ✓ 确认并评分
              </button>
              <button
                onClick={handleShowRevisionInput}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
              >
                ✏️ 修改作文
              </button>
            </div>
          </div>
        )}

        {/* 修改输入框 */}
        {showRevisionInput && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3">请输入修改意见</h3>
            <textarea
              value={revisionText}
              onChange={(e) => setRevisionText(e.target.value)}
              placeholder="请描述您希望如何修改这篇作文，例如：增加更多具体例子、调整文章结构、改变写作风格等..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
            />
            <div className="flex gap-3 mt-3">
              <button
                onClick={handleRevisionSubmit}
                disabled={!revisionText.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                提交修改意见
              </button>
              <button
                onClick={handleCancelRevision}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        )}

        {/* 输入框 */}
        {!showConfirmation && !showRevisionInput && (
          <form onSubmit={handleSubmit} className="flex gap-3">
            <input
              type="text"
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              placeholder="请输入作文主题或要求，例如：写一篇关于友谊的作文"
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isProcessing}
            />
            <button
              type="submit"
              disabled={!userInput.trim() || isProcessing}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              <Send className="w-4 h-4" />
              发送
            </button>
          </form>
        )}
      </div>

      {/* 评分结果显示 */}
      {evaluation && (
        <EvaluationDisplay evaluation={evaluation} />
      )}

      {/* 旧的确认对话框已被内联控件替代 */}
    </div>
  );
}
