import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Essay Writer - AI作文生成系统',
  description: '基于AG-UI协议和LangGraph的智能作文生成系统，支持实时流式输出、用户反馈和AI评分',
  keywords: ['AI作文', '智能写作', 'LangGraph', 'AG-UI', '作文评分'],
  authors: [{ name: 'Essay Writer Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </body>
    </html>
  )
}
