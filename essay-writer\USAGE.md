# Essay Writer 使用指南

## 🎯 项目概述

Essay Writer 是一个基于 AG-UI 协议和 LangGraph 的智能作文生成系统，具有以下特色功能：

- **🤖 智能生成**: 使用 DeepSeek 大语言模型生成高质量作文
- **⚡ 实时流式**: 基于 AG-UI 协议的实时流式输出
- **🔄 交互修改**: 支持用户反馈和多轮修改（最多3次）
- **📊 AI评分**: 专业的多维度作文评分和改进建议
- **⏰ 自动确认**: 10秒自动确认机制，操作便捷

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Docker Desktop (推荐)
- 或者 Python 3.9+ 和 Node.js 18+

### 2. 获取 API Key

1. 访问 [DeepSeek 平台](https://platform.deepseek.com/)
2. 注册并登录账号
3. 创建 API Key
4. 复制 API Key 备用

### 3. 配置项目

```bash
# 进入项目目录
cd essay-writer

# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件，添加您的 API Key
# DEEPSEEK_API_KEY=your_api_key_here
```

### 4. 启动系统

**Docker 方式 (推荐):**
```bash
# Windows
start.bat

# Linux/Mac
./start.sh
```

**开发环境方式:**
```bash
# Windows
dev-start.bat

# 手动启动
# 后端
cd backend && python -m uvicorn src.api.main:app --reload --port 8000

# 前端
cd frontend && npm run dev
```

## 📱 使用流程

### 1. 访问应用
打开浏览器访问: http://localhost:3000

### 2. 输入作文要求
在聊天框中输入您的作文主题，例如：
- "写一篇关于环保的作文"
- "我的理想，要求800字，议论文风格"
- "友谊的力量，需要包含具体例子"

### 3. 观看实时生成
- AI 会实时生成作文内容
- 可以看到生成进度和当前步骤
- 内容会逐字流式显示

### 4. 确认或修改
生成完成后，您有两个选择：
- **确认**: 10秒后自动确认，或点击"确认"按钮
- **修改**: 点击"提出修改意见"，输入具体的修改要求

### 5. 获取评分
确认后，AI 会对作文进行多维度评分：
- 主题立意 (25分)
- 结构布局 (20分)
- 语言表达 (25分)
- 内容充实 (20分)
- 创新亮点 (10分)

## 💡 使用技巧

### 输入技巧
- **明确主题**: 清楚地表达作文主题
- **具体要求**: 可以指定字数、风格、类型等
- **分行输入**: 主题和要求可以分行输入

示例输入：
```
我的理想
要求：800字左右，记叙文风格
需要包含具体的职业规划和实现步骤
```

### 修改技巧
- **具体指出问题**: 明确指出需要修改的地方
- **提供方向**: 给出具体的修改建议
- **保持主题**: 修改意见应围绕原主题

示例修改意见：
```
请在第二段增加更多具体的例子
文章结尾可以更加有力一些
语言可以更加生动形象
```

## 🔧 系统管理

### 查看日志
```bash
# Docker 方式
docker-compose logs -f

# 分别查看
docker-compose logs backend
docker-compose logs frontend
```

### 重启服务
```bash
# Docker 方式
docker-compose restart

# 停止服务
docker-compose down

# 重新构建
docker-compose up -d --build
```

### 开发调试
- 后端日志: 查看后端终端窗口
- 前端日志: 查看前端终端窗口或浏览器控制台
- API 文档: http://localhost:8000/docs

## 🐛 常见问题

### 1. API Key 相关
**问题**: "API调用失败"
**解决**: 
- 检查 `.env` 文件中的 `DEEPSEEK_API_KEY` 是否正确
- 确认 API Key 有足够的余额
- 检查网络连接

### 2. 服务启动失败
**问题**: 端口被占用
**解决**:
- 检查 8000 和 3000 端口是否被占用
- 修改 `docker-compose.yml` 中的端口映射
- 或停止占用端口的其他服务

### 3. 前端无法连接后端
**问题**: 前端显示连接错误
**解决**:
- 确认后端服务正常运行
- 检查 `next.config.js` 中的代理配置
- 查看浏览器网络面板的错误信息

### 4. 生成内容不满意
**问题**: 作文质量不高
**解决**:
- 提供更详细的要求和背景信息
- 使用修改功能进行多轮优化
- 尝试不同的表达方式

## 📊 性能优化

### 1. 提高生成速度
- 使用更简洁的输入描述
- 避免过于复杂的要求
- 合理设置作文长度

### 2. 提高生成质量
- 提供具体的写作要求
- 包含风格、结构等详细信息
- 使用修改功能进行迭代优化

## 🔒 安全注意事项

1. **API Key 保护**: 不要将 API Key 提交到版本控制系统
2. **网络安全**: 生产环境请使用 HTTPS
3. **访问控制**: 根据需要设置访问限制
4. **数据隐私**: 注意保护用户输入的隐私信息

## 📞 技术支持

如果您遇到问题：
1. 查看本文档的常见问题部分
2. 检查系统日志获取详细错误信息
3. 在项目 GitHub 页面提交 Issue
4. 联系技术支持团队

---

**祝您使用愉快！** 🎉
