"""
DeepSeek LLM客户端
"""
import os
from typing import As<PERSON><PERSON>enerator, Optional
from openai import AsyncOpenA<PERSON>
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
import logging

logger = logging.getLogger(__name__)


class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        self.api_base = os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1")
        self.model = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
        
        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")
        
        # 初始化OpenAI客户端（兼容DeepSeek API）
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.api_base
        )
        
        # 初始化LangChain客户端
        self.langchain_client = ChatOpenAI(
            api_key=self.api_key,
            base_url=self.api_base,
            model=self.model,
            temperature=0.7,
            streaming=True
        )
    
    async def generate_text(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> str:
        """生成文本（非流式）"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating text: {e}")
            raise
    
    async def generate_text_stream(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> AsyncGenerator[str, None]:
        """生成文本（流式）"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Error generating text stream: {e}")
            raise
    
    def get_langchain_client(self) -> ChatOpenAI:
        """获取LangChain客户端"""
        return self.langchain_client
    
    async def invoke_with_messages(self, messages: list) -> str:
        """使用消息列表调用模型"""
        try:
            # 转换为OpenAI格式的消息
            openai_messages = []
            for msg in messages:
                if hasattr(msg, 'type'):
                    # LangChain消息格式
                    if msg.type == 'system':
                        openai_messages.append({"role": "system", "content": msg.content})
                    elif msg.type == 'human':
                        openai_messages.append({"role": "user", "content": msg.content})
                    elif msg.type == 'ai':
                        openai_messages.append({"role": "assistant", "content": msg.content})
                else:
                    # 假设是字典格式
                    openai_messages.append(msg)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error invoking with messages: {e}")
            raise
    
    async def stream_with_messages(self, messages: list) -> AsyncGenerator[str, None]:
        """使用消息列表流式调用模型"""
        try:
            # 转换为OpenAI格式的消息
            openai_messages = []
            for msg in messages:
                if hasattr(msg, 'type'):
                    # LangChain消息格式
                    if msg.type == 'system':
                        openai_messages.append({"role": "system", "content": msg.content})
                    elif msg.type == 'human':
                        openai_messages.append({"role": "user", "content": msg.content})
                    elif msg.type == 'ai':
                        openai_messages.append({"role": "assistant", "content": msg.content})
                else:
                    # 假设是字典格式
                    openai_messages.append(msg)
            
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                temperature=0.7,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Error streaming with messages: {e}")
            raise


# 全局客户端实例
deepseek_client = None

def get_deepseek_client() -> DeepSeekClient:
    """获取DeepSeek客户端实例"""
    global deepseek_client
    if deepseek_client is None:
        deepseek_client = DeepSeekClient()
    return deepseek_client
