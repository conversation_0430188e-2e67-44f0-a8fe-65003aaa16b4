"use client";

import React from 'react';
import { 
  <PERSON><PERSON>hart3, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  Lightbulb,
  Star,
  Award
} from 'lucide-react';
import { EvaluationDisplayProps } from '../../types';

export default function EvaluationDisplay({ evaluation, className = "" }: EvaluationDisplayProps) {
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    if (score >= 60) return "text-orange-600";
    return "text-red-600";
  };

  const getScoreGrade = (score: number) => {
    if (score >= 90) return "优秀";
    if (score >= 80) return "良好";
    if (score >= 70) return "中等";
    if (score >= 60) return "及格";
    return "需要改进";
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {/* 标题 */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-100 rounded-lg">
          <BarChart3 className="w-6 h-6 text-purple-600" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-gray-900">
            📊 AI评分结果
          </h2>
          <p className="text-sm text-gray-500">
            基于多维度评价标准的智能评分
          </p>
        </div>
      </div>

      {/* 总分显示 */}
      <div className="text-center mb-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Award className="w-8 h-8 text-yellow-500" />
          <span className="text-lg font-medium text-gray-700">总分</span>
        </div>
        <div className={`evaluation-score ${getScoreColor(evaluation.overall_score)}`}>
          {evaluation.overall_score.toFixed(1)}
        </div>
        <div className="text-lg font-medium text-gray-600 mt-1">
          {getScoreGrade(evaluation.overall_score)}
        </div>
      </div>

      {/* 各项评分 */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Star className="w-5 h-5 text-yellow-500" />
          各项评分
        </h3>
        <div className="evaluation-criteria">
          {Object.entries(evaluation.criteria_scores).map(([criterion, score]) => (
            <div key={criterion} className="criteria-item">
              <span className="font-medium text-gray-700">
                {criterion}
              </span>
              <div className="flex items-center gap-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(score / 25) * 100}%` }}
                  />
                </div>
                <span className={`font-semibold ${getScoreColor(score)}`}>
                  {score.toFixed(1)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 优点 */}
      {evaluation.strengths.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-green-500" />
            ✅ 优点
          </h3>
          <ul className="space-y-2">
            {evaluation.strengths.map((strength, index) => (
              <li key={index} className="flex items-start gap-2 text-gray-700">
                <span className="text-green-500 mt-1">•</span>
                <span>{strength}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 不足 */}
      {evaluation.weaknesses.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            ⚠️ 需要改进
          </h3>
          <ul className="space-y-2">
            {evaluation.weaknesses.map((weakness, index) => (
              <li key={index} className="flex items-start gap-2 text-gray-700">
                <span className="text-orange-500 mt-1">•</span>
                <span>{weakness}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 改进建议 */}
      {evaluation.suggestions.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-blue-500" />
            💡 改进建议
          </h3>
          <ul className="space-y-2">
            {evaluation.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start gap-2 text-gray-700">
                <span className="text-blue-500 mt-1">•</span>
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 详细反馈 */}
      {evaluation.detailed_feedback && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            📝 详细反馈
          </h3>
          <div className="prose prose-sm max-w-none text-gray-700">
            {evaluation.detailed_feedback.split('\n').map((paragraph, index) => (
              <p key={index} className="mb-2">
                {paragraph}
              </p>
            ))}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-3 mt-6 pt-6 border-t">
        <button
          onClick={() => window.print()}
          className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          打印报告
        </button>
        <button
          onClick={() => {
            // 这里可以添加重新生成的逻辑
            window.location.reload();
          }}
          className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          重新生成
        </button>
      </div>
    </div>
  );
}
