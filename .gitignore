# === 项目目录忽略 ===
# 忽略整个 essay-writer 项目
essay-writer/

# === 通用忽略文件 ===
# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存目录
cache/
.cache/

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 构建输出
build/
dist/
out/
.next/

# 测试覆盖率
coverage/
.nyc_output
.coverage
htmlcov/

# 其他常见忽略项
*.pid
*.seed
*.pid.lock
.npm
.eslintcache
.node_repl_history
.yarn-integrity
