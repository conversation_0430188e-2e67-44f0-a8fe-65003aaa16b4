@echo off
chcp 65001 >nul

echo Starting Essay Writer Development Environment...

REM Check environment file
if not exist .env (
    echo Creating environment file...
    copy .env.example .env >nul
    echo Please edit .env file and add your DEEPSEEK_API_KEY
    notepad .env
    echo Press any key to continue after saving...
    pause >nul
)

echo Starting backend service...
cd backend
start "Essay Writer Backend" cmd /k "python -m uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000"

echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo Starting frontend service...
cd ..\frontend
start "Essay Writer Frontend" cmd /k "npm run dev"

echo Waiting for frontend to start...
timeout /t 10 /nobreak >nul

echo.
echo Development environment started successfully!
echo.
echo Access URLs:
echo    Frontend: http://localhost:3000
echo    Backend API: http://localhost:8000
echo    API Docs: http://localhost:8000/docs
echo.
echo Development Tips:
echo    - Backend supports hot reload
echo    - Frontend supports hot reload
echo    - Check backend logs in backend window
echo    - Check frontend logs in frontend window
echo.
echo Press any key to open browser...
pause >nul

start http://localhost:3000
