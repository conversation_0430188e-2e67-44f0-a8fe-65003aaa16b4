@echo off
chcp 65001 >nul
echo 🚀 启动 Essay Writer 开发环境...

REM 检查环境变量文件
if not exist .env (
    echo 📝 创建环境变量文件...
    copy .env.example .env >nul
    echo ⚠️  请编辑 .env 文件，添加您的 DEEPSEEK_API_KEY
    notepad .env
    echo 💡 保存后按任意键继续...
    pause >nul
)

echo 🔧 启动后端服务...
cd backend
start "Essay Writer Backend" cmd /k "python -m uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000"

echo ⏳ 等待后端启动...
timeout /t 5 /nobreak >nul

echo 🎨 启动前端服务...
cd ..\frontend
start "Essay Writer Frontend" cmd /k "npm run dev"

echo ⏳ 等待前端启动...
timeout /t 10 /nobreak >nul

echo.
echo 🎉 开发环境启动完成！
echo.
echo 📱 访问地址：
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:8000
echo    API文档:  http://localhost:8000/docs
echo.
echo 💡 开发提示：
echo    - 后端支持热重载，修改代码会自动重启
echo    - 前端支持热重载，修改代码会自动刷新
echo    - 查看后端日志请切换到后端窗口
echo    - 查看前端日志请切换到前端窗口
echo.
echo 按任意键打开浏览器...
pause >nul

start http://localhost:3000
