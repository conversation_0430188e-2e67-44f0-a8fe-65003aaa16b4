/**
 * TypeScript类型定义
 */

// 作文状态接口
export interface EssayState {
  status: {
    phase: 'idle' | 'generating' | 'waiting_confirmation' | 'revising' | 'evaluating' | 'completed' | 'error';
    error?: string;
    timestamp: string;
  };
  essay: {
    topic: string;
    requirements: string;
    content: string;
    version: number;
    completed: boolean;
  };
  revision: {
    round: number;
    max_rounds: number;
    feedback: string[];
    history: string[];
  };
  confirmation: {
    pending: boolean;
    timeout: number;
    confirmed: boolean;
    user_action?: 'confirm' | 'revise' | 'timeout';
  };
  evaluation: {
    score?: number;
    criteria: Record<string, number>;
    suggestions: string[];
    completed: boolean;
  };
  processing: {
    progress: number;
    current_step: string;
    in_progress: boolean;
  };
  ui: {
    show_progress: boolean;
    show_confirmation: boolean;
    show_evaluation: boolean;
    active_tab: string;
  };
}

// 评分结果接口
export interface EvaluationResult {
  overall_score: number;
  criteria_scores: Record<string, number>;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  detailed_feedback: string;
}

// 消息接口
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

// AG-UI事件类型
export type AGUIEventType = 
  | 'RUN_STARTED'
  | 'RUN_FINISHED'
  | 'STATE_SNAPSHOT'
  | 'STATE_DELTA'
  | 'TEXT_MESSAGE_START'
  | 'TEXT_MESSAGE_CONTENT'
  | 'TEXT_MESSAGE_END'
  | 'TOOL_CALL_START'
  | 'TOOL_CALL_END';

// AG-UI事件接口
export interface AGUIEvent {
  type: AGUIEventType;
  timestamp?: string;
  [key: string]: any;
}

// 状态更新路径
export interface StateDelta {
  path: string[];
  value: any;
}

// 进度信息
export interface ProgressInfo {
  progress: number;
  step: string;
  estimated_time?: number;
}

// 确认状态
export interface ConfirmationState {
  pending: boolean;
  timeout_seconds: number;
  remaining_time?: number;
  auto_confirm: boolean;
}

// 用户操作类型
export type UserAction = 'confirm' | 'revise' | 'cancel';

// 作文生成请求
export interface EssayGenerationRequest {
  topic: string;
  requirements?: string;
  style?: string;
  length?: string;
}

// 作文修改请求
export interface EssayRevisionRequest {
  feedback: string;
  original_content: string;
  topic: string;
}

// 组件属性接口
export interface EssayWriterProps {
  className?: string;
  onEssayGenerated?: (content: string) => void;
  onEvaluationCompleted?: (evaluation: EvaluationResult) => void;
}

export interface ProgressBarProps {
  progress: number;
  step: string;
  className?: string;
}

export interface ConfirmationDialogProps {
  isOpen: boolean;
  timeoutSeconds: number;
  onConfirm: () => void;
  onRevise: (feedback: string) => void;
  onCancel: () => void;
}

export interface EvaluationDisplayProps {
  evaluation: EvaluationResult;
  className?: string;
}

export interface StatusIndicatorProps {
  phase: EssayState['status']['phase'];
  step?: string;
  className?: string;
}

// 工具函数类型
export type FormatTimeFunction = (seconds: number) => string;
export type ValidateInputFunction = (input: string) => boolean;
export type ExtractTopicFunction = (input: string) => { topic: string; requirements: string };

// 常量
export const ESSAY_PHASES = {
  IDLE: 'idle',
  GENERATING: 'generating',
  WAITING_CONFIRMATION: 'waiting_confirmation',
  REVISING: 'revising',
  EVALUATING: 'evaluating',
  COMPLETED: 'completed',
  ERROR: 'error',
} as const;

export const DEFAULT_TIMEOUT_SECONDS = 10;
export const MAX_REVISION_ROUNDS = 3;
export const MIN_TOPIC_LENGTH = 2;
export const MAX_TOPIC_LENGTH = 200;
export const MAX_FEEDBACK_LENGTH = 500;
