"""
简化的AG-UI协议端点实现
"""
import json
import uuid
import logging
import asyncio
from typing import AsyncGenerator
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from ..utils.llm_client import get_deepseek_client

logger = logging.getLogger(__name__)

router = APIRouter()

class Message(BaseModel):
    id: str
    role: str
    content: str

class RunAgentInput(BaseModel):
    thread_id: str
    run_id: str
    messages: list[Message]

def create_sse_event(event_type: str, data: dict) -> str:
    """创建SSE事件"""
    event_data = {
        "type": event_type,
        "timestamp": "2024-01-01T00:00:00Z",
        **data
    }
    return f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"

@router.post("/essay-writer")
async def essay_writer_endpoint(input_data: RunAgentInput):
    """AG-UI协议主端点"""

    async def event_generator() -> AsyncGenerator[str, None]:
        message_id = str(uuid.uuid4())

        try:
            # 1. 发送运行开始事件
            yield create_sse_event("RUN_STARTED", {
                "thread_id": input_data.thread_id,
                "run_id": input_data.run_id
            })
            
            # 2. 获取用户消息
            user_message = ""
            for msg in input_data.messages:
                if msg.role == "user":
                    user_message = msg.content
                    break
            
            if not user_message:
                raise ValueError("没有找到用户输入")
            
            # 3. 发送状态更新
            yield create_sse_event("STATE_DELTA", {
                "message_id": message_id,
                "delta": [
                    {"path": ["status", "phase"], "value": "generating"},
                    {"path": ["processing", "progress"], "value": 0.1},
                    {"path": ["processing", "current_step"], "value": "开始生成作文..."}
                ]
            })
            
            # 4. 开始生成作文
            yield create_sse_event("TEXT_MESSAGE_START", {
                "message_id": message_id,
                "role": "assistant"
            })
            
            # 检查是否是确认或修改请求
            if user_message.lower() in ["确认", "继续", "confirm"]:
                # 生成评分
                async for chunk in generate_evaluation(message_id):
                    yield chunk
            else:
                # 生成作文
                async for chunk in generate_essay(message_id, user_message):
                    yield chunk
            
            yield create_sse_event("TEXT_MESSAGE_END", {
                "message_id": message_id
            })
            
            # 5. 发送运行结束事件
            yield create_sse_event("RUN_FINISHED", {
                "thread_id": input_data.thread_id,
                "run_id": input_data.run_id
            })
            
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            
            # 发送错误事件
            yield create_sse_event("STATE_DELTA", {
                "message_id": message_id,
                "delta": [
                    {"path": ["status", "phase"], "value": "error"},
                    {"path": ["status", "error"], "value": str(e)}
                ]
            })
            
            yield create_sse_event("TEXT_MESSAGE_START", {
                "message_id": message_id,
                "role": "assistant"
            })
            
            yield create_sse_event("TEXT_MESSAGE_CONTENT", {
                "message_id": message_id,
                "delta": f"抱歉，处理您的请求时出现错误：{str(e)}"
            })
            
            yield create_sse_event("TEXT_MESSAGE_END", {
                "message_id": message_id
            })
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Transfer-Encoding": "chunked"
        }
    )

async def generate_essay(message_id: str, topic: str) -> AsyncGenerator[str, None]:
    """生成作文"""
    try:
        client = get_deepseek_client()
        
        # 更新进度
        yield create_sse_event("STATE_DELTA", {
            "message_id": message_id,
            "delta": [
                {"path": ["processing", "progress"], "value": 0.3},
                {"path": ["processing", "current_step"], "value": "正在生成作文内容..."}
            ]
        })
        
        # 构建提示词
        system_prompt = """你是一位专业的作文写作助手，擅长根据用户的要求创作高质量的作文。

请根据用户的主题创作一篇结构清晰、内容丰富、语言优美的作文。具体要求：

📝 **篇幅要求**：
- 字数控制在800-1200字左右
- 内容充实，避免空洞的表述

🏗️ **结构要求**：
- 开头：引人入胜，点明主题（150-200字）
- 主体：分2-3个段落展开论述，每段200-300字
- 结尾：总结升华，呼应开头（100-150字）

💡 **内容要求**：
- 观点明确，论证有力
- 使用具体的事例、细节描写
- 融入个人感悟和思考
- 语言生动，富有感染力

🎨 **表达要求**：
- 用词准确，句式多样
- 适当使用修辞手法
- 段落间过渡自然
- 层次分明，逻辑清晰

请直接输出作文内容，不需要额外的解释或标题。"""

        user_prompt = f"请根据以下主题写一篇作文，要求内容丰富、篇幅充实：\n\n{topic}"
        
        # 流式生成作文
        async for chunk in client.generate_text_stream(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            temperature=0.7,
            max_tokens=2000  # 增加最大token数以生成更长的作文
        ):
            print(f"Sending chunk: {chunk[:50]}...")  # 调试信息
            yield create_sse_event("TEXT_MESSAGE_CONTENT", {
                "message_id": message_id,
                "delta": chunk
            })

            # 强制刷新缓冲区
            yield ""

            # 模拟进度更新
            await asyncio.sleep(0.05)  # 稍微增加延迟以便观察流式效果
        
        # 发送确认提示
        yield create_sse_event("TEXT_MESSAGE_CONTENT", {
            "message_id": message_id,
            "delta": "\n\n---\n\n✅ 作文已生成完成！"
        })

        # 完成生成，等待确认
        yield create_sse_event("STATE_DELTA", {
            "message_id": message_id,
            "delta": [
                {"path": ["status", "phase"], "value": "waiting_confirmation"},
                {"path": ["processing", "progress"], "value": 0.9},
                {"path": ["processing", "current_step"], "value": "作文生成完成，等待确认..."}
            ]
        })
        
    except Exception as e:
        logger.error(f"生成作文失败: {e}")
        yield create_sse_event("TEXT_MESSAGE_CONTENT", {
            "message_id": message_id,
            "delta": f"生成作文时出现错误：{str(e)}"
        })

async def generate_evaluation(message_id: str) -> AsyncGenerator[str, None]:
    """生成评分"""
    try:
        # 更新状态为评分中
        yield create_sse_event("STATE_DELTA", {
            "message_id": message_id,
            "delta": [
                {"path": ["status", "phase"], "value": "evaluating"},
                {"path": ["processing", "progress"], "value": 0.95},
                {"path": ["processing", "current_step"], "value": "正在进行AI评分..."}
            ]
        })
        
        # 模拟评分过程 - 使用简单的一次性输出
        evaluation_content = """

## 📊 作文评分结果

### 总分：85.5/100

### 各项评分：
- **主题立意**：22.0/25分 - 主题明确，立意积极向上
- **结构布局**：17.0/20分 - 结构完整，层次清晰
- **语言表达**：21.5/25分 - 语言流畅，表达生动
- **内容充实**：17.0/20分 - 内容丰富，例证恰当
- **创新亮点**：8.0/10分 - 有一定的创新性

### ✅ 优点：
- 文章结构完整，开头引人入胜
- 语言表达流畅，用词准确
- 主题鲜明，立意深刻

### ⚠️ 需要改进：
- 可以增加更多具体的例子
- 部分段落之间的过渡可以更自然
- 结尾可以更加有力

### 💡 改进建议：
- 在论述中加入更多生活实例
- 注意段落间的逻辑连接
- 结尾部分可以升华主题

### 📝 总体评价：
这是一篇质量较高的作文，主题明确，结构完整，语言表达流畅。作者能够围绕主题展开论述，内容充实。建议在具体例证和段落过渡方面进一步完善，相信会有更好的表现。
"""

        # 一次性输出评分结果
        yield create_sse_event("TEXT_MESSAGE_CONTENT", {
            "message_id": message_id,
            "delta": evaluation_content
        })
        
        # 完成评分
        yield create_sse_event("STATE_DELTA", {
            "message_id": message_id,
            "delta": [
                {"path": ["status", "phase"], "value": "completed"},
                {"path": ["processing", "progress"], "value": 1.0},
                {"path": ["processing", "current_step"], "value": "评分完成！"}
            ]
        })
        
    except Exception as e:
        logger.error(f"生成评分失败: {e}")
        yield create_sse_event("TEXT_MESSAGE_CONTENT", {
            "message_id": message_id,
            "delta": f"生成评分时出现错误：{str(e)}"
        })
