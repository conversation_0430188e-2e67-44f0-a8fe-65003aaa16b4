# ChatDB 后端架构文档

## 系统概述

ChatDB 是一个基于 FastAPI 的智能数据库查询系统，集成了自然语言处理、Text2SQL 转换、混合检索和图数据库等先进技术，为用户提供智能化的数据库交互体验。

## 系统架构图

```mermaid
graph TB
    subgraph "客户端层"
        WEB[Web 前端]
        API_CLIENT[API 客户端]
    end
    
    subgraph "网关层"
        FASTAPI[FastAPI 应用]
        CORS[CORS 中间件]
        AUTH[认证中间件]
    end
    
    subgraph "API 路由层"
        CONN[connections<br/>数据库连接管理]
        SCHEMA[schema<br/>数据库模式分析]
        QUERY[query<br/>SQL 查询执行]
        TEXT2SQL[text2sql-sse<br/>自然语言转SQL]
        HYBRID[hybrid-qa<br/>混合问答系统]
        GRAPH[graph-visualization<br/>图形可视化]
        CHAT[chat-history<br/>聊天历史]
        VALUE[value-mappings<br/>值映射管理]
        TIPS[relationship-tips<br/>关系提示]
    end
    
    subgraph "业务服务层"
        AGENT_SVC[代理编排服务<br/>agent_orchestrator]
        SCHEMA_SVC[模式分析服务<br/>schema_service]
        TEXT2SQL_SVC[Text2SQL 服务<br/>text2sql_service]
        HYBRID_SVC[混合检索服务<br/>hybrid_retrieval_service]
        WS_MGR[WebSocket 管理器<br/>websocket_manager]
        DB_SVC[数据库服务<br/>db_service]
    end
    
    subgraph "AI 代理层"
        QUERY_ANALYZER[查询分析器<br/>query_analyzer]
        SQL_GEN[SQL 生成器<br/>sql_generator]
        HYBRID_GEN[混合SQL生成器<br/>hybrid_sql_generator]
        SCHEMA_RET[模式检索器<br/>schema_retriever]
        SQL_EXEC[SQL 执行器<br/>sql_executor]
        SQL_EXP[SQL 解释器<br/>sql_explainer]
        VIS_REC[可视化推荐器<br/>visualization_recommender]
    end
    
    subgraph "数据存储层"
        SQLITE[SQLite<br/>Chinook 数据库]
        MYSQL[MySQL<br/>业务数据库]
        NEO4J[Neo4j<br/>知识图谱]
        MILVUS[Milvus<br/>向量数据库]
    end
    
    subgraph "外部服务"
        LLM[大语言模型<br/>DeepSeek/OpenAI]
        EMBED[嵌入模型<br/>Sentence Transformers]
    end
    
    WEB --> FASTAPI
    API_CLIENT --> FASTAPI
    FASTAPI --> CORS
    CORS --> AUTH
    
    AUTH --> CONN
    AUTH --> SCHEMA
    AUTH --> QUERY
    AUTH --> TEXT2SQL
    AUTH --> HYBRID
    AUTH --> GRAPH
    AUTH --> CHAT
    AUTH --> VALUE
    AUTH --> TIPS
    
    CONN --> DB_SVC
    SCHEMA --> SCHEMA_SVC
    QUERY --> DB_SVC
    TEXT2SQL --> TEXT2SQL_SVC
    TEXT2SQL --> WS_MGR
    HYBRID --> HYBRID_SVC
    GRAPH --> SCHEMA_SVC
    CHAT --> HYBRID_SVC
    VALUE --> SCHEMA_SVC
    TIPS --> SCHEMA_SVC
    
    AGENT_SVC --> QUERY_ANALYZER
    AGENT_SVC --> SQL_GEN
    AGENT_SVC --> HYBRID_GEN
    AGENT_SVC --> SCHEMA_RET
    AGENT_SVC --> SQL_EXEC
    AGENT_SVC --> SQL_EXP
    AGENT_SVC --> VIS_REC
    
    TEXT2SQL_SVC --> AGENT_SVC
    HYBRID_SVC --> AGENT_SVC
    SCHEMA_SVC --> AGENT_SVC
    
    DB_SVC --> SQLITE
    DB_SVC --> MYSQL
    HYBRID_SVC --> NEO4J
    HYBRID_SVC --> MILVUS
    
    SQL_GEN --> LLM
    HYBRID_GEN --> LLM
    SQL_EXP --> LLM
    HYBRID_SVC --> EMBED
```

## 详细目录结构

```
backend/
├── alembic/                    # 数据库迁移管理
│   ├── versions/              # 迁移版本文件
│   └── env.py                 # Alembic 环境配置
├── app/                       # 主应用目录
│   ├── agents/                # AI 代理模块 (10个文件)
│   │   ├── base.py           # 代理基类定义
│   │   ├── factory.py        # 代理工厂模式
│   │   ├── hybrid_sql_generator.py    # 混合SQL生成器
│   │   ├── query_analyzer.py          # 查询分析器
│   │   ├── schema_retriever.py        # 模式检索器
│   │   ├── sql_executor.py            # SQL执行器
│   │   ├── sql_explainer.py           # SQL解释器
│   │   ├── sql_generator.py           # SQL生成器
│   │   ├── visualization_recommender.py # 可视化推荐器
│   │   └── types.py                   # 类型定义
│   ├── api/                   # API 路由层
│   │   └── api_v1/           # API v1 版本
│   │       ├── api.py        # 主路由配置
│   │       └── endpoints/    # 具体端点实现 (12个文件)
│   │           ├── connections.py      # 数据库连接管理
│   │           ├── schema.py          # 数据库模式查询
│   │           ├── query.py           # SQL查询执行
│   │           ├── text2sql_sse.py    # Text2SQL流式响应
│   │           ├── hybrid_qa.py       # 混合问答系统
│   │           ├── graph_visualization.py # 图形可视化
│   │           ├── chat_history.py    # 聊天历史管理
│   │           ├── value_mappings.py  # 值映射管理
│   │           ├── relationship_tips.py # 关系提示
│   │           └── websocket_manager.py # WebSocket管理
│   ├── core/                  # 核心配置模块
│   │   ├── config.py         # 应用配置管理
│   │   ├── exceptions.py     # 异常处理
│   │   ├── llms.py          # 大语言模型配置
│   │   ├── security.py      # 安全配置
│   │   └── utils.py         # 工具函数
│   ├── services/              # 业务服务层 (12个文件)
│   │   ├── agent_orchestrator.py      # 代理编排服务
│   │   ├── schema_service.py          # 模式分析服务
│   │   ├── text2sql_service.py        # Text2SQL服务
│   │   ├── hybrid_retrieval_service.py # 混合检索服务
│   │   ├── websocket_manager.py       # WebSocket管理服务
│   │   ├── db_service.py              # 数据库服务
│   │   ├── schema_utils.py            # 模式工具函数
│   │   └── text2sql_utils.py          # Text2SQL工具函数
│   ├── crud/                  # 数据访问层
│   ├── db/                    # 数据库连接管理
│   ├── models/                # 数据模型定义
│   ├── schemas/               # Pydantic 模型
│   └── tests/                 # 测试用例
├── alembic.ini               # Alembic 配置文件
├── Dockerfile                # Docker 构建文件
├── main.py                   # 应用入口点
├── init_db.py               # 数据库初始化脚本
├── init_hybrid_system.py    # 混合系统初始化脚本
└── requirements.txt         # 项目依赖清单
```

## 核心模块详细说明

### 1. API 路由层 (app/api/api_v1/endpoints/)

#### connections.py (4.2KB, 143行)
- **功能**: 数据库连接管理
- **主要接口**:
  - `GET /connections/` - 获取所有数据库连接
  - `POST /connections/` - 创建新的数据库连接
  - `PUT /connections/{id}` - 更新数据库连接
  - `DELETE /connections/{id}` - 删除数据库连接
  - `POST /connections/test` - 测试数据库连接

#### schema.py (20KB, 507行)
- **功能**: 数据库模式分析和查询
- **主要接口**:
  - `GET /schema/{connection_id}/tables` - 获取数据库表列表
  - `GET /schema/{connection_id}/table/{table_name}` - 获取表结构详情
  - `GET /schema/{connection_id}/relationships` - 获取表关系信息
  - `POST /schema/analyze` - 深度分析数据库模式

#### text2sql_sse.py (31KB, 763行)
- **功能**: 自然语言转SQL的流式处理
- **特性**:
  - Server-Sent Events (SSE) 支持
  - 实时流式响应
  - 支持复杂查询分析
  - 集成混合检索系统
- **主要接口**:
  - `POST /text2sql-sse/generate` - 生成SQL查询（流式）
  - `POST /text2sql-sse/explain` - 解释SQL查询
  - `POST /text2sql-sse/optimize` - 优化SQL查询

#### hybrid_qa.py (12KB, 362行)
- **功能**: 混合问答系统
- **特性**:
  - 结合向量检索和图数据库
  - 支持上下文感知问答
  - 自动学习和模式发现
- **主要接口**:
  - `POST /hybrid-qa/ask` - 混合问答查询
  - `POST /hybrid-qa/feedback` - 用户反馈学习
  - `GET /hybrid-qa/patterns` - 获取查询模式

#### graph_visualization.py (5.6KB, 152行)
- **功能**: 数据库关系图可视化
- **主要接口**:
  - `GET /graph-visualization/{connection_id}` - 获取数据库关系图
  - `POST /graph-visualization/custom` - 自定义图形生成

#### chat_history.py (7.1KB, 221行)
- **功能**: 聊天历史管理
- **主要接口**:
  - `GET /chat-history/` - 获取聊天历史
  - `POST /chat-history/` - 保存聊天记录
  - `DELETE /chat-history/{id}` - 删除聊天记录

### 2. 业务服务层 (app/services/)

#### hybrid_retrieval_service.py (31KB, 838行)
- **功能**: 混合检索引擎核心实现
- **主要组件**:
  - `HybridRetrievalEngine`: 混合检索引擎主类
  - `VectorService`: 向量检索服务
  - `MilvusService`: Milvus向量数据库服务
  - `EnhancedNeo4jService`: 增强的Neo4j图数据库服务
- **特性**:
  - 语义检索 (35% 权重)
  - 结构化检索 (35% 权重)
  - 模式检索 (20% 权重)
  - 质量评估 (10% 权重)

#### schema_service.py (37KB, 805行)
- **功能**: 数据库模式分析服务
- **主要功能**:
  - 数据库表结构分析
  - 外键关系检测
  - 数据类型推断
  - 索引分析
  - 统计信息收集

#### text2sql_service.py (5.6KB, 170行)
- **功能**: Text2SQL转换服务
- **集成组件**:
  - 查询分析器
  - SQL生成器
  - 混合SQL生成器
  - SQL执行器

#### agent_orchestrator.py (5.9KB, 159行)
- **功能**: AI代理编排服务
- **职责**:
  - 协调多个AI代理
  - 管理代理生命周期
  - 处理代理间通信

### 3. AI 代理层 (app/agents/)

#### base.py (10KB, 283行)
- **功能**: 代理基类定义
- **提供**:
  - 统一的代理接口
  - 生命周期管理
  - 错误处理机制

#### query_analyzer.py (8.5KB, 237行)
- **功能**: 查询意图分析
- **能力**:
  - 自然语言理解
  - 查询类型识别
  - 实体提取
  - 意图分类

#### sql_generator.py (6.0KB, 123行)
- **功能**: 基础SQL生成
- **特性**:
  - 模板化SQL生成
  - 语法验证
  - 参数绑定

#### hybrid_sql_generator.py (15KB, 357行)
- **功能**: 混合SQL生成器
- **特性**:
  - 结合检索结果生成SQL
  - 上下文感知生成
  - 复杂查询支持

#### visualization_recommender.py (10KB, 246行)
- **功能**: 可视化推荐
- **能力**:
  - 根据数据类型推荐图表
  - 自动配置可视化参数
  - 支持多种图表类型

### 4. 核心配置 (app/core/)

#### config.py (2.8KB, 71行)
- **配置项**:
  - 数据库连接配置 (MySQL, Neo4j)
  - LLM配置 (OpenAI/DeepSeek)
  - Milvus向量数据库配置
  - 混合检索权重配置
  - 学习系统配置
  - 性能优化配置

#### utils.py (5.6KB, 177行)
- **工具函数**:
  - 数据处理工具
  - 文本处理函数
  - 缓存管理
  - 日志工具

## 技术栈详解

### 核心框架
- **FastAPI 0.115.12+**: 现代化的Python Web框架
- **Uvicorn 0.34.2+**: ASGI服务器
- **SQLAlchemy 2.0.40+**: Python ORM框架
- **Pydantic 2.11.4+**: 数据验证和序列化

### 数据库技术
- **SQLite**: Chinook示例数据库
- **MySQL**: 主要业务数据库
- **Neo4j 5.28.1+**: 图数据库，存储知识图谱
- **Milvus 2.3.0+**: 向量数据库，支持语义检索

### AI/ML 技术栈
- **OpenAI 1.76.2+**: 大语言模型API
- **Sentence Transformers 2.2.0+**: 文本嵌入模型
- **AutoGen 0.5.7+**: 多代理系统框架
- **PyTorch 1.9.0+**: 深度学习框架
- **Scikit-learn 1.0.0+**: 机器学习库

### 通信和实时功能
- **WebSockets 15.0.1+**: WebSocket支持
- **SSE-Starlette 1.6.5+**: Server-Sent Events

### 数据处理
- **Pandas 2.2.3+**: 数据分析库
- **NumPy 1.21.0+**: 数值计算库
- **NetworkX 2.8.0+**: 图分析库

## 系统特性

### 1. 混合检索系统
- **多维度检索**: 语义、结构化、模式、质量四个维度
- **自适应权重**: 根据查询类型动态调整权重
- **实时学习**: 基于用户反馈持续优化

### 2. 智能SQL生成
- **上下文感知**: 结合数据库模式和历史查询
- **多策略生成**: 模板化、规则化、AI生成相结合
- **质量保证**: 语法检查、执行验证、性能优化

### 3. 实时交互
- **流式响应**: SSE支持实时数据流
- **WebSocket**: 双向实时通信
- **异步处理**: 高并发支持

### 4. 可扩展架构
- **微服务设计**: 模块化、松耦合
- **插件化代理**: 易于扩展新的AI能力
- **配置驱动**: 灵活的配置管理

## 部署架构

### Docker 部署
```dockerfile
# 基于 Python 3.9
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 环境配置
```bash
# 数据库配置
MYSQL_SERVER=localhost
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DB=chatdb

# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# LLM配置
OPENAI_API_KEY=your_api_key
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530

# 混合检索配置
SEMANTIC_WEIGHT=0.35
STRUCTURAL_WEIGHT=0.35
PATTERN_WEIGHT=0.20
QUALITY_WEIGHT=0.10
```

## 开发指南

### 1. 环境搭建
```bash
# 克隆项目
git clone <repository_url>
cd chatdb/backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库初始化
```bash
# 初始化基础数据库
python init_db.py

# 初始化混合检索系统
python init_hybrid_system.py
```

### 3. 运行开发服务器
```bash
# 开发模式运行
python main.py

# 或使用uvicorn直接运行
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. API 文档访问
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 5. 测试
```bash
# 运行测试
pytest app/tests/

# 运行特定测试
pytest app/tests/test_text2sql.py -v
```

## 性能优化

### 1. 数据库优化
- 连接池管理
- 查询缓存
- 索引优化
- 分页查询

### 2. AI服务优化
- 模型缓存
- 批量处理
- 异步调用
- 结果缓存

### 3. 系统优化
- 内存管理
- 并发控制
- 资源池化
- 监控告警

## 监控和日志

### 日志配置
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

### 关键指标监控
- API响应时间
- 数据库连接数
- 内存使用率
- SQL生成成功率
- 用户查询频率 