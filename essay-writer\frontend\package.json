{"name": "essay-writer-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@copilotkit/react-core": "^1.3.18", "@copilotkit/react-ui": "^1.3.18", "@copilotkit/runtime": "^1.3.18", "@ag-ui/client": "^0.1.0", "next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.7", "autoprefixer": "^10.4.19", "postcss": "^8.4.40", "lucide-react": "^0.427.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-next": "14.2.5", "@tailwindcss/typography": "^0.5.13"}, "engines": {"node": ">=18.0.0"}}