"use client";

import React, { useState } from 'react';
import { X, Clock, CheckCircle, Edit3 } from 'lucide-react';
import { ConfirmationDialogProps } from '../../types';

export default function ConfirmationDialog({
  isOpen,
  timeoutSeconds,
  onConfirm,
  onRevise,
  onCancel
}: ConfirmationDialogProps) {
  const [feedback, setFeedback] = useState('');
  const [showFeedbackInput, setShowFeedbackInput] = useState(false);

  if (!isOpen) return null;

  const handleReviseClick = () => {
    setShowFeedbackInput(true);
  };

  const handleSubmitFeedback = () => {
    if (feedback.trim()) {
      onRevise(feedback.trim());
      setFeedback('');
      setShowFeedbackInput(false);
    }
  };

  const handleCancel = () => {
    setFeedback('');
    setShowFeedbackInput(false);
    onCancel();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            确认作文内容
          </h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6">
          {!showFeedbackInput ? (
            <>
              {/* 倒计时提示 */}
              <div className="flex items-center gap-2 mb-4 p-3 bg-yellow-50 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-600" />
                <span className="text-yellow-800">
                  {timeoutSeconds}秒后将自动确认
                </span>
              </div>

              {/* 说明文字 */}
              <p className="text-gray-600 mb-6">
                作文已生成完成，您可以选择：
              </p>

              {/* 操作按钮 */}
              <div className="space-y-3">
                <button
                  onClick={onConfirm}
                  className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <CheckCircle className="w-5 h-5" />
                  确认并继续评分
                </button>
                
                <button
                  onClick={handleReviseClick}
                  className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <Edit3 className="w-5 h-5" />
                  提出修改意见
                </button>
              </div>
            </>
          ) : (
            <>
              {/* 修改意见输入 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  请输入您的修改意见：
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="例如：请增加更多具体的例子，或者调整文章的结构..."
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  maxLength={500}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {feedback.length}/500 字符
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-3">
                <button
                  onClick={() => setShowFeedbackInput(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  返回
                </button>
                <button
                  onClick={handleSubmitFeedback}
                  disabled={!feedback.trim()}
                  className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  提交修改意见
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
