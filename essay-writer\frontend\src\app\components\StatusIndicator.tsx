"use client";

import React from 'react';
import { 
  Clock, 
  Edit3, 
  CheckCircle, 
  AlertCircle, 
  Sparkles, 
  BarChart3,
  <PERSON>ader2 
} from 'lucide-react';
import { StatusIndicatorProps, ESSAY_PHASES } from '../../types';

export default function StatusIndicator({ phase, step, className = "" }: StatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (phase) {
      case ESSAY_PHASES.GENERATING:
        return {
          icon: <Sparkles className="w-4 h-4" />,
          text: "正在生成作文",
          className: "status-generating"
        };
      case ESSAY_PHASES.WAITING_CONFIRMATION:
        return {
          icon: <Clock className="w-4 h-4" />,
          text: "等待确认",
          className: "status-waiting"
        };
      case ESSAY_PHASES.REVISING:
        return {
          icon: <Edit3 className="w-4 h-4" />,
          text: "正在修改",
          className: "status-revising"
        };
      case ESSAY_PHASES.EVALUATING:
        return {
          icon: <BarChart3 className="w-4 h-4" />,
          text: "正在评分",
          className: "status-evaluating"
        };
      case ESSAY_PHASES.COMPLETED:
        return {
          icon: <CheckCircle className="w-4 h-4" />,
          text: "已完成",
          className: "status-completed"
        };
      case ESSAY_PHASES.ERROR:
        return {
          icon: <AlertCircle className="w-4 h-4" />,
          text: "处理出错",
          className: "status-error"
        };
      default:
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          text: "准备中",
          className: "status-indicator"
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`status-indicator ${config.className} ${className}`}>
      {config.icon}
      <span className="font-medium">
        {config.text}
      </span>
      {step && (
        <span className="text-xs opacity-75">
          - {step}
        </span>
      )}
    </div>
  );
}
