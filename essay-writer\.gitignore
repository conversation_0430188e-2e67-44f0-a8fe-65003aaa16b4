# === 通用忽略文件 ===
# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# === 前端 (Next.js/React) ===
# 依赖
frontend/node_modules/
frontend/.pnp
frontend/.pnp.js

# 测试
frontend/coverage/

# Next.js 构建输出
frontend/.next/
frontend/out/

# 生产构建
frontend/build/
frontend/dist/

# 环境变量
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Vercel
frontend/.vercel

# TypeScript
frontend/*.tsbuildinfo
frontend/next-env.d.ts

# === 后端 (Python/FastAPI) ===
# Python 字节码
backend/__pycache__/
backend/*.py[cod]
backend/*$py.class

# 分发 / 打包
backend/.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/share/python-wheels/
backend/*.egg-info/
backend/.installed.cfg
backend/*.egg
backend/MANIFEST

# PyInstaller
backend/*.manifest
backend/*.spec

# 单元测试 / 覆盖率报告
backend/htmlcov/
backend/.tox/
backend/.nox/
backend/.coverage
backend/.coverage.*
backend/.cache
backend/nosetests.xml
backend/coverage.xml
backend/*.cover
backend/*.py,cover
backend/.hypothesis/
backend/.pytest_cache/
backend/cover/

# 翻译
backend/*.mo
backend/*.pot

# Django
backend/*.log
backend/local_settings.py
backend/db.sqlite3
backend/db.sqlite3-journal

# Flask
backend/instance/
backend/.webassets-cache

# Scrapy
backend/.scrapy

# Sphinx 文档
backend/docs/_build/

# PyBuilder
backend/.pybuilder/
backend/target/

# Jupyter Notebook
backend/.ipynb_checkpoints

# IPython
backend/profile_default/
backend/ipython_config.py

# pyenv
backend/.python-version

# pipenv
backend/Pipfile.lock

# poetry
backend/poetry.lock

# pdm
backend/.pdm.toml

# PEP 582
backend/__pypackages__/

# Celery
backend/celerybeat-schedule
backend/celerybeat.pid

# SageMath 解析文件
backend/*.sage.py

# 环境变量
backend/.env
backend/.venv
backend/env/
backend/venv/
backend/ENV/
backend/env.bak/
backend/venv.bak/

# Spyder 项目设置
backend/.spyderproject
backend/.spyproject

# Rope 项目设置
backend/.ropeproject

# mkdocs 文档
backend/site/

# mypy
backend/.mypy_cache/
backend/.dmypy.json
backend/dmypy.json

# Pyre 类型检查器
backend/.pyre/

# pytype 静态类型分析器
backend/.pytype/

# Cython 调试符号
backend/cython_debug/

# PyCharm
backend/.idea/

# === 项目特定文件 ===
# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 配置文件（如果包含敏感信息）
config.json
secrets.json

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传文件
uploads/
media/

# 缓存目录
cache/
.cache/
