"use client";

import React from 'react';
import { PenTool, Sparkles, BarChart3, RefreshCw } from 'lucide-react';

export default function Header() {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo和标题 */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <PenTool className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Essay Writer
              </h1>
              <p className="text-sm text-gray-500">
                AI智能作文生成系统
              </p>
            </div>
          </div>

          {/* 功能特色 */}
          <div className="hidden md:flex items-center gap-6">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Sparkles className="w-4 h-4 text-yellow-500" />
              <span>智能生成</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <RefreshCw className="w-4 h-4 text-green-500" />
              <span>实时修改</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <BarChart3 className="w-4 h-4 text-purple-500" />
              <span>AI评分</span>
            </div>
          </div>

          {/* 版本信息 */}
          <div className="hidden lg:block">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                v1.0.0
              </div>
              <div className="text-xs text-gray-500">
                AG-UI + LangGraph
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
