"""
LangGraph工作流定义
"""
import logging
from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from ..models.schemas import EssayState
from .essay_generator import EssayGeneratorAgent
from .essay_evaluator import EssayEvaluatorAgent

logger = logging.getLogger(__name__)


class EssayWorkflowState(Dict[str, Any]):
    """工作流状态类型"""
    pass


class EssayWorkflow:
    """作文生成和评分工作流"""
    
    def __init__(self):
        self.generator = EssayGeneratorAgent()
        self.evaluator = EssayEvaluatorAgent()
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流"""
        # 创建状态图
        workflow = StateGraph(EssayWorkflowState)
        
        # 添加节点
        workflow.add_node("parse_input", self._parse_input)
        workflow.add_node("generate_essay", self._generate_essay)
        workflow.add_node("wait_confirmation", self._wait_confirmation)
        workflow.add_node("revise_essay", self._revise_essay)
        workflow.add_node("evaluate_essay", self._evaluate_essay)
        workflow.add_node("finalize", self._finalize)
        
        # 设置入口点
        workflow.set_entry_point("parse_input")
        
        # 添加边
        workflow.add_edge("parse_input", "generate_essay")
        workflow.add_edge("generate_essay", "wait_confirmation")
        
        # 条件边：确认或修改
        workflow.add_conditional_edges(
            "wait_confirmation",
            self._should_revise,
            {
                "revise": "revise_essay",
                "evaluate": "evaluate_essay",
                "wait": "wait_confirmation"
            }
        )
        
        workflow.add_edge("revise_essay", "wait_confirmation")
        workflow.add_edge("evaluate_essay", "finalize")
        workflow.add_edge("finalize", END)
        
        return workflow.compile()
    
    async def _parse_input(self, state: EssayWorkflowState) -> EssayWorkflowState:
        """解析用户输入"""
        try:
            logger.info("解析用户输入")
            
            # 获取用户消息
            messages = state.get("messages", [])
            if not messages:
                raise ValueError("没有找到用户消息")
            
            user_message = None
            for msg in messages:
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break
            
            if not user_message:
                raise ValueError("没有找到用户输入内容")
            
            # 提取主题和要求
            parsed = self.generator.extract_topic_and_requirements(user_message)
            
            # 更新状态
            state.update({
                "topic": parsed["topic"],
                "requirements": parsed["requirements"],
                "original_input": user_message,
                "current_step": "input_parsed",
                "revision_count": 0,
                "max_revisions": 3
            })
            
            logger.info(f"解析完成 - 主题: {parsed['topic']}")
            return state
            
        except Exception as e:
            logger.error(f"解析输入失败: {e}")
            state["error"] = str(e)
            return state
    
    async def _generate_essay(self, state: EssayWorkflowState) -> EssayWorkflowState:
        """生成作文"""
        try:
            logger.info("开始生成作文")
            
            topic = state.get("topic", "")
            requirements = state.get("requirements", "")
            
            if not topic:
                raise ValueError("缺少作文主题")
            
            # 生成作文（同步版本，用于工作流）
            essay_content = await self.generator.generate_essay_sync(
                topic=topic,
                requirements=requirements
            )
            
            # 更新状态
            state.update({
                "essay_content": essay_content,
                "current_step": "essay_generated",
                "generation_completed": True
            })
            
            logger.info("作文生成完成")
            return state
            
        except Exception as e:
            logger.error(f"生成作文失败: {e}")
            state["error"] = str(e)
            return state
    
    async def _wait_confirmation(self, state: EssayWorkflowState) -> EssayWorkflowState:
        """等待用户确认"""
        try:
            logger.info("等待用户确认")
            
            # 这个节点主要用于状态管理
            # 实际的确认逻辑在API层处理
            state.update({
                "current_step": "waiting_confirmation",
                "confirmation_pending": True
            })
            
            return state
            
        except Exception as e:
            logger.error(f"等待确认失败: {e}")
            state["error"] = str(e)
            return state
    
    async def _revise_essay(self, state: EssayWorkflowState) -> EssayWorkflowState:
        """修改作文"""
        try:
            logger.info("开始修改作文")
            
            original_content = state.get("essay_content", "")
            feedback = state.get("user_feedback", "")
            topic = state.get("topic", "")
            revision_count = state.get("revision_count", 0)
            
            if not original_content or not feedback:
                raise ValueError("缺少原始内容或用户反馈")
            
            # 修改作文
            revised_content = await self.generator.revise_essay_sync(
                original_content=original_content,
                feedback=feedback,
                topic=topic
            )
            
            # 更新状态
            state.update({
                "essay_content": revised_content,
                "current_step": "essay_revised",
                "revision_count": revision_count + 1,
                "confirmation_pending": True,
                "user_feedback": None  # 清除反馈
            })
            
            logger.info(f"作文修改完成，修改次数: {revision_count + 1}")
            return state
            
        except Exception as e:
            logger.error(f"修改作文失败: {e}")
            state["error"] = str(e)
            return state
    
    async def _evaluate_essay(self, state: EssayWorkflowState) -> EssayWorkflowState:
        """评价作文"""
        try:
            logger.info("开始评价作文")
            
            essay_content = state.get("essay_content", "")
            topic = state.get("topic", "")
            requirements = state.get("requirements", "")
            
            if not essay_content:
                raise ValueError("缺少作文内容")
            
            # 评价作文
            evaluation = await self.evaluator.evaluate_essay_sync(
                content=essay_content,
                topic=topic,
                requirements=requirements
            )
            
            # 更新状态
            state.update({
                "evaluation": evaluation.dict(),
                "current_step": "essay_evaluated",
                "evaluation_completed": True
            })
            
            logger.info(f"作文评价完成，总分: {evaluation.overall_score}")
            return state
            
        except Exception as e:
            logger.error(f"评价作文失败: {e}")
            state["error"] = str(e)
            return state
    
    async def _finalize(self, state: EssayWorkflowState) -> EssayWorkflowState:
        """完成工作流"""
        try:
            logger.info("完成工作流")
            
            state.update({
                "current_step": "completed",
                "workflow_completed": True
            })
            
            return state
            
        except Exception as e:
            logger.error(f"完成工作流失败: {e}")
            state["error"] = str(e)
            return state
    
    def _should_revise(self, state: EssayWorkflowState) -> str:
        """判断是否需要修改"""
        # 检查是否有错误
        if state.get("error"):
            return "evaluate"  # 有错误时直接评价
        
        # 检查是否达到最大修改次数
        revision_count = state.get("revision_count", 0)
        max_revisions = state.get("max_revisions", 3)
        if revision_count >= max_revisions:
            return "evaluate"
        
        # 检查用户操作
        user_action = state.get("user_action")
        if user_action == "confirm":
            return "evaluate"
        elif user_action == "revise" and state.get("user_feedback"):
            return "revise"
        else:
            return "wait"  # 继续等待
    
    async def run_workflow(self, initial_state: Dict[str, Any]) -> Dict[str, Any]:
        """运行工作流"""
        try:
            logger.info("开始运行工作流")
            
            # 执行工作流
            result = await self.graph.ainvoke(initial_state)
            
            logger.info("工作流执行完成")
            return result
            
        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            raise
    
    def get_current_step(self, state: Dict[str, Any]) -> str:
        """获取当前步骤"""
        return state.get("current_step", "unknown")
    
    def is_waiting_for_confirmation(self, state: Dict[str, Any]) -> bool:
        """检查是否在等待确认"""
        return state.get("confirmation_pending", False)
    
    def is_completed(self, state: Dict[str, Any]) -> bool:
        """检查是否已完成"""
        return state.get("workflow_completed", False)


# 全局工作流实例
essay_workflow = None

def get_essay_workflow() -> EssayWorkflow:
    """获取工作流实例"""
    global essay_workflow
    if essay_workflow is None:
        essay_workflow = EssayWorkflow()
    return essay_workflow
