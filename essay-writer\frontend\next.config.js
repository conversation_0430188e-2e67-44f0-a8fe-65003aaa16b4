/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  env: {
    NEXT_PUBLIC_COPILOTKIT_RUNTIME_URL: process.env.NEXT_PUBLIC_COPILOTKIT_RUNTIME_URL || '/api/copilotkit',
  },
  async rewrites() {
    return [
      {
        source: '/api/essay-writer',
        destination: 'http://localhost:8000/api/essay-writer',
      },
      {
        source: '/api/copilotkit/:path*',
        destination: 'http://localhost:8000/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
