#!/bin/bash

# Essay Writer 启动脚本

set -e

echo "🚀 启动 Essay Writer 系统..."

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️  未找到 .env 文件，正在创建..."
    cp .env.example .env
    echo "📝 请编辑 .env 文件，添加您的 DEEPSEEK_API_KEY"
    echo "💡 然后重新运行此脚本"
    exit 1
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查API密钥
if grep -q "your_deepseek_api_key_here" .env; then
    echo "❌ 请在 .env 文件中设置您的 DEEPSEEK_API_KEY"
    exit 1
fi

echo "🔧 构建和启动服务..."

# 构建并启动服务
docker-compose up -d --build

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查后端服务
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务启动失败"
    docker-compose logs backend
    exit 1
fi

# 检查前端服务
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务启动失败"
    docker-compose logs frontend
    exit 1
fi

echo ""
echo "🎉 Essay Writer 系统启动成功！"
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8000"
echo "   API文档:  http://localhost:8000/docs"
echo ""
echo "🛠️  管理命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "💡 使用提示："
echo "   1. 在前端页面的聊天框中输入作文主题"
echo "   2. AI会实时生成作文内容"
echo "   3. 10秒内确认或提出修改意见"
echo "   4. 点击继续获取AI评分"
echo ""
