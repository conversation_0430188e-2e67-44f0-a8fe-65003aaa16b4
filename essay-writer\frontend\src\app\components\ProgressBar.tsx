"use client";

import React from 'react';
import { ProgressBarProps } from '../../types';

export default function ProgressBar({ progress, step, className = "" }: ProgressBarProps) {
  const percentage = Math.min(Math.max(progress * 100, 0), 100);

  return (
    <div className={`space-y-2 ${className}`}>
      {/* 进度信息 */}
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-700 font-medium">
          {step || "处理中..."}
        </span>
        <span className="text-gray-500">
          {percentage.toFixed(0)}%
        </span>
      </div>

      {/* 进度条 */}
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: `${percentage}%` }}
        />
      </div>

      {/* 进度阶段指示器 */}
      <div className="flex justify-between text-xs text-gray-400">
        <span className={percentage >= 10 ? "text-blue-600" : ""}>
          开始
        </span>
        <span className={percentage >= 30 ? "text-blue-600" : ""}>
          生成中
        </span>
        <span className={percentage >= 70 ? "text-blue-600" : ""}>
          完善中
        </span>
        <span className={percentage >= 90 ? "text-blue-600" : ""}>
          等待确认
        </span>
        <span className={percentage >= 100 ? "text-green-600" : ""}>
          完成
        </span>
      </div>
    </div>
  );
}
