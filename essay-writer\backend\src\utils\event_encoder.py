"""
AG-UI协议事件编码器
"""
import json
import logging
from typing import Any, Dict
from ..models.schemas import Base<PERSON>vent

logger = logging.getLogger(__name__)


class EventEncoder:
    """AG-UI协议事件编码器"""
    
    def encode(self, event: BaseEvent) -> str:
        """编码事件为SSE格式"""
        try:
            # 将事件转换为字典
            if hasattr(event, 'dict'):
                event_data = event.dict()
            elif hasattr(event, 'model_dump'):
                event_data = event.model_dump()
            else:
                event_data = event.__dict__
            
            # 转换为JSON字符串
            json_data = json.dumps(event_data, ensure_ascii=False)
            
            # 格式化为SSE格式
            sse_data = f"data: {json_data}\n\n"
            
            logger.debug(f"编码事件: {event.type}")
            return sse_data
            
        except Exception as e:
            logger.error(f"编码事件失败: {e}")
            # 返回错误事件
            error_event = {
                "type": "ERROR",
                "error": str(e),
                "timestamp": event.timestamp if hasattr(event, 'timestamp') else None
            }
            json_data = json.dumps(error_event, ensure_ascii=False)
            return f"data: {json_data}\n\n"
    
    def encode_dict(self, event_dict: Dict[str, Any]) -> str:
        """编码字典为SSE格式"""
        try:
            json_data = json.dumps(event_dict, ensure_ascii=False)
            sse_data = f"data: {json_data}\n\n"
            return sse_data
        except Exception as e:
            logger.error(f"编码字典失败: {e}")
            error_event = {
                "type": "ERROR",
                "error": str(e)
            }
            json_data = json.dumps(error_event, ensure_ascii=False)
            return f"data: {json_data}\n\n"
    
    def encode_text_chunk(self, message_id: str, text_chunk: str) -> str:
        """编码文本块为TEXT_MESSAGE_CONTENT事件"""
        try:
            event = {
                "type": "TEXT_MESSAGE_CONTENT",
                "message_id": message_id,
                "delta": text_chunk
            }
            return self.encode_dict(event)
        except Exception as e:
            logger.error(f"编码文本块失败: {e}")
            return ""
    
    def encode_progress_update(self, message_id: str, progress: float, step: str) -> str:
        """编码进度更新为STATE_DELTA事件"""
        try:
            event = {
                "type": "STATE_DELTA",
                "message_id": message_id,
                "delta": [
                    {
                        "path": ["processing", "progress"],
                        "value": progress
                    },
                    {
                        "path": ["processing", "current_step"],
                        "value": step
                    }
                ]
            }
            return self.encode_dict(event)
        except Exception as e:
            logger.error(f"编码进度更新失败: {e}")
            return ""
    
    def encode_status_update(self, message_id: str, phase: str, **kwargs) -> str:
        """编码状态更新为STATE_DELTA事件"""
        try:
            delta = [
                {
                    "path": ["status", "phase"],
                    "value": phase
                }
            ]
            
            # 添加额外的状态更新
            for key, value in kwargs.items():
                delta.append({
                    "path": ["status", key],
                    "value": value
                })
            
            event = {
                "type": "STATE_DELTA",
                "message_id": message_id,
                "delta": delta
            }
            return self.encode_dict(event)
        except Exception as e:
            logger.error(f"编码状态更新失败: {e}")
            return ""
