"""
作文生成代理
"""
import json
import logging
from typing import Dict, Any, AsyncGenerator
from langchain_core.messages import HumanMessage, SystemMessage
from ..utils.llm_client import get_deepseek_client
from ..utils.prompts import (
    ESSAY_GENERATION_SYSTEM_PROMPT,
    ESSAY_GENERATION_USER_PROMPT,
    ESSAY_REVISION_SYSTEM_PROMPT,
    ESSAY_REVISION_USER_PROMPT
)
from ..models.schemas import EssayGenerationRequest, EssayRevisionRequest

logger = logging.getLogger(__name__)


class EssayGeneratorAgent:
    """作文生成代理"""
    
    def __init__(self):
        self.llm_client = get_deepseek_client()
    
    async def generate_essay(
        self, 
        topic: str, 
        requirements: str = "", 
        style: str = "通用", 
        length: str = "中等"
    ) -> AsyncGenerator[str, None]:
        """生成作文（流式输出）"""
        try:
            logger.info(f"开始生成作文，主题：{topic}")
            
            # 构建提示词
            user_prompt = ESSAY_GENERATION_USER_PROMPT.format(
                topic=topic,
                requirements=requirements or "无特殊要求",
                style=style,
                length=length
            )
            
            # 流式生成作文
            async for chunk in self.llm_client.generate_text_stream(
                system_prompt=ESSAY_GENERATION_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=0.7
            ):
                yield chunk
                
        except Exception as e:
            logger.error(f"作文生成失败: {e}")
            raise
    
    async def revise_essay(
        self, 
        original_content: str, 
        feedback: str, 
        topic: str
    ) -> AsyncGenerator[str, None]:
        """修改作文（流式输出）"""
        try:
            logger.info(f"开始修改作文，反馈：{feedback[:50]}...")
            
            # 构建修改提示词
            user_prompt = ESSAY_REVISION_USER_PROMPT.format(
                topic=topic,
                original_content=original_content,
                feedback=feedback
            )
            
            # 流式生成修改后的作文
            async for chunk in self.llm_client.generate_text_stream(
                system_prompt=ESSAY_REVISION_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=0.7
            ):
                yield chunk
                
        except Exception as e:
            logger.error(f"作文修改失败: {e}")
            raise
    
    async def generate_essay_sync(
        self, 
        topic: str, 
        requirements: str = "", 
        style: str = "通用", 
        length: str = "中等"
    ) -> str:
        """生成作文（同步版本）"""
        try:
            user_prompt = ESSAY_GENERATION_USER_PROMPT.format(
                topic=topic,
                requirements=requirements or "无特殊要求",
                style=style,
                length=length
            )
            
            return await self.llm_client.generate_text(
                system_prompt=ESSAY_GENERATION_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=0.7
            )
        except Exception as e:
            logger.error(f"作文生成失败: {e}")
            raise
    
    async def revise_essay_sync(
        self, 
        original_content: str, 
        feedback: str, 
        topic: str
    ) -> str:
        """修改作文（同步版本）"""
        try:
            user_prompt = ESSAY_REVISION_USER_PROMPT.format(
                topic=topic,
                original_content=original_content,
                feedback=feedback
            )
            
            return await self.llm_client.generate_text(
                system_prompt=ESSAY_REVISION_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=0.7
            )
        except Exception as e:
            logger.error(f"作文修改失败: {e}")
            raise
    
    def extract_topic_and_requirements(self, user_input: str) -> Dict[str, str]:
        """从用户输入中提取主题和要求"""
        try:
            # 简单的关键词提取逻辑
            # 在实际应用中，可以使用更复杂的NLP技术
            
            # 检查是否包含明确的要求关键词
            requirement_keywords = ["要求", "需要", "应该", "必须", "字数", "风格", "类型"]
            
            lines = user_input.strip().split('\n')
            topic = lines[0].strip()  # 第一行作为主题
            requirements = ""
            
            # 如果有多行或包含要求关键词，提取要求
            if len(lines) > 1:
                requirements = '\n'.join(lines[1:]).strip()
            elif any(keyword in user_input for keyword in requirement_keywords):
                # 尝试分离主题和要求
                for keyword in requirement_keywords:
                    if keyword in user_input:
                        parts = user_input.split(keyword, 1)
                        if len(parts) == 2:
                            topic = parts[0].strip()
                            requirements = keyword + parts[1].strip()
                        break
            
            return {
                "topic": topic or user_input,  # 如果提取失败，使用原始输入作为主题
                "requirements": requirements
            }
            
        except Exception as e:
            logger.error(f"提取主题和要求失败: {e}")
            return {
                "topic": user_input,
                "requirements": ""
            }
    
    def estimate_generation_time(self, topic: str, requirements: str) -> int:
        """估算生成时间（秒）"""
        # 基于内容长度估算生成时间
        base_time = 10  # 基础时间
        content_length = len(topic) + len(requirements)
        
        if content_length > 100:
            return base_time + 10
        elif content_length > 50:
            return base_time + 5
        else:
            return base_time
