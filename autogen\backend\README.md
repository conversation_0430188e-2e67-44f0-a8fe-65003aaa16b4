# ChatDB Backend

ChatDB Backend is an intelligent Text2SQL system built with Python, FastAPI, and advanced AI technologies. It features a multi-agent architecture, hybrid retrieval system, and real-time streaming capabilities for natural language database querying.

## Features

### Core Capabilities
- **RESTful API** for comprehensive database connection management
- **Advanced Schema Discovery** with intelligent relationship detection
- **AI-Powered Text2SQL** conversion using large language models
- **Hybrid Retrieval System** combining semantic and structural search
- **Multi-Agent Architecture** with specialized AI agents for different tasks
- **Real-time Streaming** with Server-Sent Events (SSE) and WebSocket support
- **Value Mapping** for natural language term translation
- **Interactive Graph Visualization** of database relationships

### AI & ML Features
- **Semantic Search** using sentence transformers and vector databases
- **Knowledge Graph** integration with Neo4j for relationship understanding
- **Auto-Learning System** that improves from user interactions
- **Pattern Discovery** for query optimization
- **Intelligent SQL Explanation** and optimization recommendations

## Technology Stack

### Core Framework
- **FastAPI 0.115.12+** - Modern Python web framework
- **Uvicorn 0.34.2+** - ASGI server
- **SQLAlchemy 2.0.40+** - Python ORM framework
- **Pydantic 2.11.4+** - Data validation and serialization

### Database Technologies
- **MySQL** - Primary business database
- **Neo4j 5.28.1+** - Graph database for knowledge graphs
- **Milvus 2.3.0+** - Vector database for semantic search
- **SQLite** - Chinook demo database

### AI/ML Stack
- **OpenAI 1.76.2+** - Large language model API (supports DeepSeek)
- **AutoGen 0.5.7+** - Multi-agent system framework
- **Sentence Transformers 2.2.0+** - Text embedding models
- **PyTorch 1.9.0+** - Deep learning framework
- **Scikit-learn 1.0.0+** - Machine learning library

### Communication & Real-time
- **WebSockets 15.0.1+** - Real-time bidirectional communication
- **SSE-Starlette 1.6.5+** - Server-Sent Events for streaming

## Getting Started

### Prerequisites

- **Python 3.9+**
- **MySQL 8.0+**
- **Neo4j 4.4+**
- **Milvus 2.3.0+** (optional, for semantic search)
- **OpenAI API Key** or **DeepSeek API Key**

### Quick Start with Docker (Recommended)

1. **Clone the repository:**
   ```bash
   git clone <repository_url>
   cd chatdb
   ```

2. **Create environment file:**
   ```bash
   # Create .env file in the root directory
   echo "OPENAI_API_KEY=your_api_key_here" > .env
   ```

3. **Start all services:**
   ```bash
   docker-compose up -d
   ```

4. **Initialize the system:**
   ```bash
   # Initialize basic database
   docker-compose exec backend python init_db.py

   # Initialize hybrid retrieval system (optional)
   docker-compose exec backend python init_hybrid_system.py
   ```

5. **Access the application:**
   - **API Documentation**: http://localhost:8000/docs
   - **Frontend**: http://localhost:3000
   - **Neo4j Browser**: http://localhost:7474

### Manual Installation

1. **Create a virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up databases:**
   ```sql
   -- Create MySQL database
   CREATE DATABASE chatdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

4. **Configure environment:**
   Create a `.env` file in the backend directory:
   ```bash
   # Database Configuration
   MYSQL_SERVER=localhost
   MYSQL_USER=root
   MYSQL_PASSWORD=your_password
   MYSQL_DB=chatdb
   MYSQL_PORT=3306

   # Neo4j Configuration
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USER=neo4j
   NEO4J_PASSWORD=your_password

   # LLM Configuration
   OPENAI_API_KEY=your_api_key
   OPENAI_API_BASE=https://api.deepseek.com/v1  # Optional: for DeepSeek
   LLM_MODEL=deepseek-chat  # or gpt-4

   # Milvus Configuration (optional)
   MILVUS_HOST=localhost
   MILVUS_PORT=19530

   # Hybrid Retrieval Configuration
   SEMANTIC_WEIGHT=0.35
   STRUCTURAL_WEIGHT=0.35
   PATTERN_WEIGHT=0.20
   QUALITY_WEIGHT=0.10
   ```

5. **Initialize databases:**
   ```bash
   # Initialize basic database schema
   python init_db.py

   # Initialize hybrid retrieval system (optional)
   python init_hybrid_system.py
   ```

6. **Start the development server:**
   ```bash
   # Method 1: Direct execution
   python main.py

   # Method 2: Using uvicorn
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

## Project Structure

```
backend/
├── alembic/                    # Database migration management
│   ├── versions/              # Migration version files
│   └── env.py                 # Alembic environment configuration
├── app/                       # Main application directory
│   ├── agents/                # AI Agent modules (10 files)
│   │   ├── base.py           # Base agent class
│   │   ├── factory.py        # Agent factory pattern
│   │   ├── hybrid_sql_generator.py    # Hybrid SQL generator
│   │   ├── query_analyzer.py          # Query intent analyzer
│   │   ├── schema_retriever.py        # Schema retrieval agent
│   │   ├── sql_executor.py            # SQL execution agent
│   │   ├── sql_explainer.py           # SQL explanation agent
│   │   ├── sql_generator.py           # Basic SQL generator
│   │   ├── visualization_recommender.py # Visualization recommender
│   │   └── types.py                   # Type definitions
│   ├── api/                   # API routing layer
│   │   └── api_v1/           # API v1 implementation
│   │       ├── api.py        # Main router configuration
│   │       └── endpoints/    # API endpoints (12 files)
│   │           ├── connections.py      # Database connection management
│   │           ├── schema.py          # Database schema analysis
│   │           ├── query.py           # SQL query execution
│   │           ├── text2sql_sse.py    # Text2SQL streaming
│   │           ├── hybrid_qa.py       # Hybrid Q&A system
│   │           ├── graph_visualization.py # Graph visualization
│   │           ├── chat_history.py    # Chat history management
│   │           ├── value_mappings.py  # Value mapping management
│   │           └── relationship_tips.py # Relationship tips
│   ├── core/                  # Core configuration modules
│   │   ├── config.py         # Application configuration
│   │   ├── exceptions.py     # Exception handling
│   │   ├── llms.py          # LLM client configuration
│   │   ├── security.py      # Security configuration
│   │   └── utils.py         # Utility functions
│   ├── services/              # Business service layer (12 files)
│   │   ├── agent_orchestrator.py      # Agent orchestration service
│   │   ├── schema_service.py          # Schema analysis service
│   │   ├── text2sql_service.py        # Text2SQL service
│   │   ├── hybrid_retrieval_service.py # Hybrid retrieval engine
│   │   ├── websocket_manager.py       # WebSocket management
│   │   ├── db_service.py              # Database service
│   │   ├── schema_utils.py            # Schema utilities
│   │   └── text2sql_utils.py          # Text2SQL utilities
│   ├── crud/                  # Data access layer
│   │   ├── base.py           # Base CRUD operations
│   │   ├── crud_db_connection.py      # Database connection CRUD
│   │   ├── crud_schema_*.py           # Schema-related CRUD
│   │   └── crud_chat_history.py       # Chat history CRUD
│   ├── db/                    # Database connection management
│   │   ├── base.py           # Database base configuration
│   │   ├── session.py        # Database session management
│   │   └── init_db.py        # Database initialization
│   ├── models/                # SQLAlchemy data models
│   │   ├── db_connection.py  # Database connection model
│   │   ├── schema_*.py       # Schema-related models
│   │   └── chat_history.py   # Chat history model
│   ├── schemas/               # Pydantic schemas for API
│   │   ├── db_connection.py  # Connection schemas
│   │   ├── text2sql.py       # Text2SQL schemas
│   │   ├── query.py          # Query schemas
│   │   └── *.py              # Other API schemas
│   └── tests/                 # Test cases
│       ├── test_api.py       # API tests
│       └── test_text2sql.py  # Text2SQL tests
├── main.py                   # Application entry point
├── init_db.py               # Database initialization script
├── init_hybrid_system.py    # Hybrid system initialization
├── requirements.txt         # Python dependencies
├── Dockerfile               # Docker build configuration
└── alembic.ini             # Alembic configuration
```

## System Architecture

ChatDB Backend follows a layered architecture with clear separation of concerns:

### 1. API Layer (`app/api/`)
- **RESTful endpoints** for all system operations
- **Server-Sent Events (SSE)** for real-time streaming
- **WebSocket support** for bidirectional communication
- **Comprehensive error handling** and validation

### 2. Business Service Layer (`app/services/`)
- **Agent Orchestrator**: Coordinates multiple AI agents
- **Hybrid Retrieval Service**: Combines semantic and structural search
- **Schema Service**: Analyzes and manages database schemas
- **Text2SQL Service**: Converts natural language to SQL

### 3. AI Agent Layer (`app/agents/`)
- **Query Analyzer**: Understands user intent and extracts entities
- **SQL Generator**: Creates SQL queries from natural language
- **Hybrid SQL Generator**: Advanced SQL generation with context
- **Schema Retriever**: Finds relevant schema information
- **SQL Executor**: Safely executes SQL queries
- **SQL Explainer**: Provides human-readable explanations
- **Visualization Recommender**: Suggests appropriate charts

### 4. Data Layer (`app/models/`, `app/crud/`)
- **SQLAlchemy models** for structured data
- **CRUD operations** for data access
- **Database session management**
- **Migration support** with Alembic

## Key Features

### Hybrid Retrieval System
The system combines multiple retrieval strategies:
- **Semantic Retrieval (35%)**: Uses sentence transformers for meaning-based search
- **Structural Retrieval (35%)**: Leverages database schema relationships
- **Pattern Retrieval (20%)**: Learns from historical query patterns
- **Quality Assessment (10%)**: Evaluates and ranks results

### Multi-Agent Architecture
Specialized AI agents handle different aspects of query processing:
- **Modular design** allows easy extension and maintenance
- **Agent coordination** ensures optimal workflow
- **Fault tolerance** with graceful degradation
- **Performance optimization** through parallel processing

### Real-time Streaming
- **Server-Sent Events** for live query processing updates
- **WebSocket connections** for interactive sessions
- **Asynchronous processing** for better user experience
- **Progress tracking** for long-running operations

## API Documentation

Once the server is running, you can access the interactive API documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Key API Endpoints

#### Database Connections
- `GET /api/connections/` - List all database connections
- `POST /api/connections/` - Create a new database connection
- `PUT /api/connections/{id}` - Update a database connection
- `DELETE /api/connections/{id}` - Delete a database connection
- `POST /api/connections/test` - Test database connectivity

#### Schema Analysis
- `GET /api/schema/{connection_id}/tables` - Get database tables
- `GET /api/schema/{connection_id}/table/{table_name}` - Get table details
- `GET /api/schema/{connection_id}/relationships` - Get table relationships
- `POST /api/schema/analyze` - Perform deep schema analysis

#### Text2SQL (Streaming)
- `POST /api/text2sql-sse/generate` - Generate SQL from natural language (SSE)
- `POST /api/text2sql-sse/explain` - Explain generated SQL
- `POST /api/text2sql-sse/optimize` - Optimize SQL queries

#### Hybrid Q&A System
- `POST /api/hybrid-qa/ask` - Ask questions using hybrid retrieval
- `POST /api/hybrid-qa/feedback` - Provide feedback for learning
- `GET /api/hybrid-qa/patterns` - Get discovered query patterns

#### Graph Visualization
- `GET /api/graph-visualization/{connection_id}` - Get database relationship graph
- `POST /api/graph-visualization/custom` - Generate custom visualizations

## Configuration Options

The system supports extensive configuration through environment variables:

### Database Configuration
```bash
# MySQL Configuration
MYSQL_SERVER=localhost          # MySQL server host
MYSQL_USER=root                # MySQL username
MYSQL_PASSWORD=password        # MySQL password
MYSQL_DB=chatdb               # MySQL database name
MYSQL_PORT=3306               # MySQL port

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687  # Neo4j connection URI
NEO4J_USER=neo4j                # Neo4j username
NEO4J_PASSWORD=password         # Neo4j password
```

### AI/ML Configuration
```bash
# LLM Configuration
OPENAI_API_KEY=your_api_key           # OpenAI or DeepSeek API key
OPENAI_API_BASE=https://api.deepseek.com/v1  # API base URL (optional)
LLM_MODEL=deepseek-chat               # Model name

# Embedding Configuration
EMBEDDING_MODEL=all-MiniLM-L6-v2      # Sentence transformer model
VECTOR_DIMENSION=384                  # Vector dimension

# Milvus Configuration
MILVUS_HOST=localhost                 # Milvus server host
MILVUS_PORT=19530                    # Milvus server port
```

### Hybrid Retrieval Configuration
```bash
# Retrieval Weights (must sum to 1.0)
SEMANTIC_WEIGHT=0.35             # Semantic search weight
STRUCTURAL_WEIGHT=0.35           # Structural search weight
PATTERN_WEIGHT=0.20              # Pattern matching weight
QUALITY_WEIGHT=0.10              # Quality assessment weight

# Learning Configuration
AUTO_LEARNING_ENABLED=true       # Enable automatic learning
FEEDBACK_LEARNING_ENABLED=true   # Enable feedback-based learning
PATTERN_DISCOVERY_ENABLED=true   # Enable pattern discovery
```

### Performance Configuration
```bash
# Cache Configuration
RETRIEVAL_CACHE_TTL=3600         # Cache TTL in seconds
MAX_EXAMPLES_PER_QUERY=5         # Maximum examples per query
PARALLEL_RETRIEVAL=true          # Enable parallel retrieval

# System Configuration
SECRET_KEY=your_secret_key       # Application secret key
```

## Usage Examples

### Basic Text2SQL Query
```python
import requests

# Send a natural language query
response = requests.post(
    "http://localhost:8000/api/text2sql-sse/generate",
    json={
        "connection_id": 1,
        "question": "Show me the top 5 customers by total sales",
        "use_hybrid_retrieval": True
    },
    headers={"Accept": "text/event-stream"}
)

# Process streaming response
for line in response.iter_lines():
    if line:
        print(line.decode('utf-8'))
```

### Hybrid Q&A System
```python
# Ask a complex question
response = requests.post(
    "http://localhost:8000/api/hybrid-qa/ask",
    json={
        "connection_id": 1,
        "question": "What are the sales trends for electronic products in the last quarter?",
        "context": {
            "previous_queries": ["show me all products", "list product categories"]
        }
    }
)

result = response.json()
print(f"SQL: {result['sql']}")
print(f"Explanation: {result['explanation']}")
print(f"Confidence: {result['confidence']}")
```

### Schema Analysis
```python
# Analyze database schema
response = requests.post(
    "http://localhost:8000/api/schema/analyze",
    json={
        "connection_id": 1,
        "deep_analysis": True,
        "include_statistics": True
    }
)

schema_info = response.json()
print(f"Tables: {len(schema_info['tables'])}")
print(f"Relationships: {len(schema_info['relationships'])}")
```

## Testing

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run all tests
pytest app/tests/ -v

# Run specific test files
pytest app/tests/test_text2sql.py -v
pytest app/tests/test_api.py -v

# Run tests with coverage
pytest app/tests/ --cov=app --cov-report=html
```

### Test Categories
- **Unit Tests**: Test individual components and functions
- **Integration Tests**: Test API endpoints and database operations
- **Agent Tests**: Test AI agent functionality and coordination
- **Performance Tests**: Test system performance under load

## Deployment

### Docker Deployment (Recommended)
```bash
# Build and start all services
docker-compose up -d

# Scale backend service
docker-compose up -d --scale backend=3

# View logs
docker-compose logs -f backend

# Stop services
docker-compose down
```

### Production Deployment
```bash
# Install production dependencies
pip install gunicorn

# Run with Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --timeout 120 \
  --keep-alive 5

# Or use the provided Dockerfile
docker build -t chatdb-backend .
docker run -p 8000:8000 chatdb-backend
```

### Environment-Specific Configuration
```bash
# Development
export ENVIRONMENT=development
export DEBUG=true

# Production
export ENVIRONMENT=production
export DEBUG=false
export WORKERS=4
```

## Performance Optimization

### Database Optimization
- **Connection Pooling**: Configure appropriate pool sizes
- **Query Optimization**: Use indexes and optimize SQL queries
- **Caching**: Enable Redis for query result caching
- **Pagination**: Implement pagination for large result sets

### AI Service Optimization
- **Model Caching**: Cache model responses to reduce API calls
- **Batch Processing**: Process multiple queries together
- **Async Operations**: Use async/await for non-blocking operations
- **Resource Management**: Monitor memory and CPU usage

### System Optimization
```python
# Example configuration for production
SQLALCHEMY_POOL_SIZE = 20
SQLALCHEMY_MAX_OVERFLOW = 30
SQLALCHEMY_POOL_TIMEOUT = 30
SQLALCHEMY_POOL_RECYCLE = 3600

# Redis caching
REDIS_URL = "redis://localhost:6379/0"
CACHE_TTL = 3600

# Async settings
ASYNC_POOL_SIZE = 100
MAX_CONCURRENT_REQUESTS = 50
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check MySQL connection
mysql -h localhost -u root -p chatdb

# Check Neo4j connection
cypher-shell -a bolt://localhost:7687 -u neo4j -p password

# Check Milvus connection
python -c "from pymilvus import connections; connections.connect(host='localhost', port='19530')"
```

#### LLM API Issues
```bash
# Test OpenAI/DeepSeek API
curl -X POST "https://api.deepseek.com/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-chat", "messages": [{"role": "user", "content": "Hello"}]}'
```

#### Memory Issues
```bash
# Monitor memory usage
docker stats chatdb-backend

# Check Python memory usage
python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"
```

### Debugging
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Use debug mode
uvicorn main:app --reload --log-level debug
```

## Monitoring and Logging

### Application Metrics
- **Response Time**: API endpoint performance
- **Error Rate**: Failed requests and exceptions
- **Database Performance**: Query execution time
- **AI Service Usage**: LLM API calls and costs

### Health Checks
```bash
# Basic health check
curl http://localhost:8000/health

# Database health check
curl http://localhost:8000/api/connections/health

# AI service health check
curl http://localhost:8000/api/text2sql-sse/health
```

### Log Configuration
```python
# Configure structured logging
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "json": {
            "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default",
        },
        "file": {
            "class": "logging.FileHandler",
            "filename": "app.log",
            "formatter": "json",
        },
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "file"],
    },
}
```

## Contributing

We welcome contributions to ChatDB Backend! Please follow these guidelines:

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Install development dependencies: `pip install -r requirements-dev.txt`
4. Make your changes and add tests
5. Run tests: `pytest`
6. Submit a pull request

### Code Style
- Follow PEP 8 guidelines
- Use type hints where appropriate
- Add docstrings to functions and classes
- Keep functions small and focused

### Testing Requirements
- Write unit tests for new functionality
- Ensure all tests pass before submitting
- Maintain test coverage above 80%
- Add integration tests for API endpoints

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation at `/docs`
- Review the API documentation at `/docs` or `/redoc`

## Changelog

### Version 0.2.0 (Current)
- Added hybrid retrieval system
- Implemented multi-agent architecture
- Added real-time streaming support
- Enhanced schema analysis capabilities
- Improved performance and caching

### Version 0.1.0
- Initial release
- Basic Text2SQL functionality
- Database connection management
- Schema discovery


