version: '3.8'

services:
  # 后端服务
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: essay-writer-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_API_BASE=https://api.deepseek.com/v1
      - DEEPSEEK_MODEL=deepseek-chat
      - AUTO_CONFIRM_TIMEOUT=10
      - MAX_REVISION_ROUNDS=3
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: essay-writer-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_COPILOTKIT_RUNTIME_URL=/api/copilotkit
      - NEXT_PUBLIC_API_BASE_URL=http://backend:8000
      - NEXT_PUBLIC_APP_NAME=Essay Writer
      - NEXT_PUBLIC_APP_VERSION=1.0.0
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: essay-writer-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    profiles:
      - production

volumes:
  backend_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  default:
    name: essay-writer-network
