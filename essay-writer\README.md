# Essay Writer - AG-UI + LangGraph Project

基于AG-UI协议和LangGraph的智能作文生成系统，支持实时流式输出、用户反馈和AI评分。

## 功能特性

### 核心功能
- **智能作文生成**: 基于用户输入的主题或要求生成作文
- **实时流式输出**: 使用AG-UI协议实现实时内容流式传输
- **交互式修改**: 支持用户反馈和作文重新生成
- **自动确认机制**: 10秒自动确认或用户手动操作
- **AI智能评分**: 生成完成后提供详细的作文评分和建议

### 技术特性
- **AG-UI协议**: 标准化的代理-用户交互协议
- **LangGraph工作流**: 多步骤作文生成和评分流程
- **DeepSeek集成**: 使用DeepSeek大语言模型
- **状态管理**: 实时状态同步和进度跟踪
- **响应式UI**: 现代化的用户界面

## 技术栈

### 后端
- **FastAPI**: 现代Python Web框架
- **LangGraph**: AI工作流编排框架
- **AG-UI Protocol**: 代理交互协议
- **DeepSeek API**: 大语言模型服务
- **Pydantic**: 数据验证和序列化

### 前端
- **Next.js 14**: React全栈框架
- **CopilotKit**: AI应用开发工具包
- **AG-UI Client**: AG-UI协议客户端
- **Tailwind CSS**: 实用优先的CSS框架
- **TypeScript**: 类型安全的JavaScript

## 项目结构

```
essay-writer/
├── backend/                    # 后端服务
│   ├── src/
│   │   ├── agents/            # LangGraph代理
│   │   │   ├── essay_generator.py    # 作文生成代理
│   │   │   ├── essay_evaluator.py   # 作文评分代理
│   │   │   └── workflow.py           # 工作流定义
│   │   ├── api/               # API端点
│   │   │   ├── main.py        # FastAPI应用
│   │   │   └── endpoints.py   # AG-UI端点
│   │   ├── models/            # 数据模型
│   │   │   └── schemas.py     # Pydantic模型
│   │   └── utils/             # 工具函数
│   │       ├── llm_client.py  # DeepSeek客户端
│   │       └── prompts.py     # 提示词模板
│   ├── requirements.txt       # Python依赖
│   └── .env.example          # 环境变量示例
├── frontend/                  # 前端应用
│   ├── src/
│   │   ├── app/              # Next.js应用
│   │   │   ├── api/          # API路由
│   │   │   ├── components/   # React组件
│   │   │   └── page.tsx      # 主页面
│   │   ├── types/            # TypeScript类型
│   │   └── utils/            # 工具函数
│   ├── package.json          # Node.js依赖
│   └── tailwind.config.js    # Tailwind配置
└── docker-compose.yml        # Docker编排文件
```

## 快速开始

### 环境要求
- **Python 3.9+**
- **Node.js 18+**
- **DeepSeek API Key** (必需)
- **Docker & Docker Compose** (推荐)

### 方式一：Docker 一键启动 (推荐)

1. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env

   # 编辑 .env 文件，添加您的 DeepSeek API Key
   # DEEPSEEK_API_KEY=your_api_key_here
   ```

2. **一键启动**
   ```bash
   # Windows
   start.bat

   # Linux/Mac
   ./start.sh
   ```

3. **访问应用**
   - 前端应用: http://localhost:3000
   - 后端API: http://localhost:8000
   - API文档: http://localhost:8000/docs

### 方式二：开发环境启动

1. **后端设置**
   ```bash
   cd backend
   pip install -r requirements.txt

   # 配置环境变量
   cp .env.example .env
   # 编辑 .env 文件，添加 DEEPSEEK_API_KEY
   ```

2. **前端设置**
   ```bash
   cd frontend
   npm install
   ```

3. **启动服务**
   ```bash
   # Windows 开发环境一键启动
   dev-start.bat

   # 或手动启动
   # 终端1: 启动后端
   cd backend
   python -m uvicorn src.api.main:app --reload --port 8000

   # 终端2: 启动前端
   cd frontend
   npm run dev
   ```

### 获取 DeepSeek API Key

1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在 API Keys 页面创建新的 API Key
4. 将 API Key 添加到 `.env` 文件中

## 使用流程

1. **输入作文要求**: 在聊天框中输入作文主题或具体要求
2. **生成作文**: 系统自动生成作文并实时流式输出
3. **确认或修改**: 
   - 10秒后自动确认
   - 或输入修改意见重新生成
4. **获取评分**: 点击"继续"按钮获取AI评分和建议

## API文档

### AG-UI端点
- `POST /essay-writer` - 作文生成和评分的主要端点

### 事件类型
- `RUN_STARTED` - 工作流开始
- `STATE_SNAPSHOT` - 状态快照
- `STATE_DELTA` - 状态增量更新
- `TEXT_MESSAGE_*` - 文本消息事件
- `RUN_FINISHED` - 工作流结束

## 配置选项

### 环境变量
```bash
# DeepSeek配置
DEEPSEEK_API_KEY=your_api_key_here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# 应用配置
AUTO_CONFIRM_TIMEOUT=10  # 自动确认超时时间(秒)
MAX_REVISION_ROUNDS=3    # 最大修改轮数
```

## 开发指南

### 添加新的提示词模板
在 `backend/src/utils/prompts.py` 中添加新的模板。

### 自定义工作流
修改 `backend/src/agents/workflow.py` 中的LangGraph工作流定义。

### 扩展前端组件
在 `frontend/src/app/components/` 中添加新的React组件。

## 部署

### Docker部署
```bash
docker-compose up -d
```

### 生产环境配置
- 设置适当的环境变量
- 配置反向代理
- 启用HTTPS
- 设置日志记录

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交Issue和Pull Request！

## 支持

如有问题，请创建GitHub Issue或联系开发团队。
