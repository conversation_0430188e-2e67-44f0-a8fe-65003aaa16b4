"""
作文评分代理
"""
import json
import logging
from typing import Dict, Any, AsyncGenerator
from ..utils.llm_client import get_deepseek_client
from ..utils.prompts import (
    ESSAY_EVALUATION_SYSTEM_PROMPT,
    ESSAY_EVALUATION_USER_PROMPT
)
from ..models.schemas import EvaluationResult

logger = logging.getLogger(__name__)


class EssayEvaluatorAgent:
    """作文评分代理"""
    
    def __init__(self):
        self.llm_client = get_deepseek_client()
    
    async def evaluate_essay(
        self, 
        content: str, 
        topic: str, 
        requirements: str = ""
    ) -> AsyncGenerator[str, None]:
        """评价作文（流式输出）"""
        try:
            logger.info(f"开始评价作文，主题：{topic}")
            
            # 构建评价提示词
            user_prompt = ESSAY_EVALUATION_USER_PROMPT.format(
                topic=topic,
                requirements=requirements or "无特殊要求",
                content=content
            )
            
            # 流式生成评价结果
            async for chunk in self.llm_client.generate_text_stream(
                system_prompt=ESSAY_EVALUATION_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=0.3  # 评分使用较低的温度以保持一致性
            ):
                yield chunk
                
        except Exception as e:
            logger.error(f"作文评价失败: {e}")
            raise
    
    async def evaluate_essay_sync(
        self, 
        content: str, 
        topic: str, 
        requirements: str = ""
    ) -> EvaluationResult:
        """评价作文（同步版本，返回结构化结果）"""
        try:
            user_prompt = ESSAY_EVALUATION_USER_PROMPT.format(
                topic=topic,
                requirements=requirements or "无特殊要求",
                content=content
            )
            
            # 生成评价结果
            result = await self.llm_client.generate_text(
                system_prompt=ESSAY_EVALUATION_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=0.3
            )
            
            # 尝试解析JSON结果
            try:
                evaluation_data = json.loads(result)
                return EvaluationResult(**evaluation_data)
            except json.JSONDecodeError:
                # 如果JSON解析失败，创建一个基本的评价结果
                logger.warning("评价结果JSON解析失败，使用默认格式")
                return self._create_fallback_evaluation(result, content)
                
        except Exception as e:
            logger.error(f"作文评价失败: {e}")
            raise
    
    def _create_fallback_evaluation(self, raw_result: str, content: str) -> EvaluationResult:
        """创建备用评价结果"""
        # 简单的评分逻辑作为备用
        content_length = len(content)
        
        # 基于内容长度的简单评分
        if content_length > 800:
            base_score = 85
        elif content_length > 500:
            base_score = 80
        elif content_length > 300:
            base_score = 75
        else:
            base_score = 70
        
        return EvaluationResult(
            overall_score=base_score,
            criteria_scores={
                "主题立意": base_score * 0.25,
                "结构布局": base_score * 0.20,
                "语言表达": base_score * 0.25,
                "内容充实": base_score * 0.20,
                "创新亮点": base_score * 0.10
            },
            strengths=["内容完整", "表达清晰"],
            weaknesses=["可进一步完善"],
            suggestions=["可以增加更多具体例子", "注意段落间的过渡"],
            detailed_feedback=raw_result
        )
    
    def _extract_score_from_text(self, text: str) -> float:
        """从文本中提取分数"""
        import re
        
        # 查找分数模式
        score_patterns = [
            r'总分[：:]\s*(\d+(?:\.\d+)?)',
            r'得分[：:]\s*(\d+(?:\.\d+)?)',
            r'评分[：:]\s*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)分'
        ]
        
        for pattern in score_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        return 75.0  # 默认分数
    
    def _extract_criteria_scores(self, text: str) -> Dict[str, float]:
        """从文本中提取各项评分"""
        criteria = ["主题立意", "结构布局", "语言表达", "内容充实", "创新亮点"]
        scores = {}
        
        for criterion in criteria:
            pattern = f'{criterion}[：:]\\s*(\\d+(?:\\.\\d+)?)'
            match = re.search(pattern, text)
            if match:
                try:
                    scores[criterion] = float(match.group(1))
                except ValueError:
                    scores[criterion] = 15.0
            else:
                scores[criterion] = 15.0
        
        return scores
    
    def _extract_list_items(self, text: str, section_name: str) -> list:
        """从文本中提取列表项"""
        import re
        
        # 查找指定部分
        pattern = f'{section_name}[：:]([^\\n]*(?:\\n[^\\n]*)*?)(?=\\n\\n|\\n[^\\s]|$)'
        match = re.search(pattern, text, re.MULTILINE)
        
        if match:
            content = match.group(1).strip()
            # 提取列表项
            items = re.findall(r'[•·-]\\s*([^\\n]+)', content)
            if not items:
                # 如果没有找到列表标记，按行分割
                items = [line.strip() for line in content.split('\n') if line.strip()]
            return items[:3]  # 最多返回3项
        
        return ["待完善"]
    
    def format_evaluation_for_display(self, evaluation: EvaluationResult) -> str:
        """格式化评价结果用于显示"""
        result = f"""
## 📊 作文评分结果

### 总分：{evaluation.overall_score:.1f}/100

### 各项评分：
"""
        for criterion, score in evaluation.criteria_scores.items():
            result += f"- **{criterion}**：{score:.1f}分\n"
        
        result += f"""
### ✅ 优点：
"""
        for strength in evaluation.strengths:
            result += f"- {strength}\n"
        
        result += f"""
### ⚠️ 不足：
"""
        for weakness in evaluation.weaknesses:
            result += f"- {weakness}\n"
        
        result += f"""
### 💡 改进建议：
"""
        for suggestion in evaluation.suggestions:
            result += f"- {suggestion}\n"
        
        result += f"""
### 📝 详细反馈：
{evaluation.detailed_feedback}
"""
        
        return result
