"""
Pydantic数据模型定义
"""
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime


class Message(BaseModel):
    """消息模型"""
    id: str
    role: Literal["user", "assistant"]
    content: str
    timestamp: Optional[datetime] = None


class RunAgentInput(BaseModel):
    """AG-UI协议输入模型"""
    thread_id: str
    run_id: str
    messages: List[Message]
    tools: List[Dict[str, Any]] = []
    context: List[Dict[str, Any]] = []
    forwarded_props: Dict[str, Any] = {}
    state: Dict[str, Any] = {}


class EssayState(BaseModel):
    """作文生成状态模型"""
    # 工作流状态
    status: Dict[str, Any] = Field(default_factory=lambda: {
        "phase": "idle",  # idle, generating, waiting_confirmation, revising, evaluating, completed
        "error": None,
        "timestamp": datetime.now().isoformat()
    })
    
    # 作文内容
    essay: Dict[str, Any] = Field(default_factory=lambda: {
        "topic": "",
        "requirements": "",
        "content": "",
        "version": 1,
        "completed": False
    })
    
    # 修改历史
    revision: Dict[str, Any] = Field(default_factory=lambda: {
        "round": 0,
        "max_rounds": 3,
        "feedback": [],
        "history": []
    })
    
    # 确认状态
    confirmation: Dict[str, Any] = Field(default_factory=lambda: {
        "pending": False,
        "timeout": 10,
        "confirmed": False,
        "user_action": None  # "confirm", "revise", "timeout"
    })
    
    # 评分结果
    evaluation: Dict[str, Any] = Field(default_factory=lambda: {
        "score": None,
        "criteria": {},
        "suggestions": [],
        "completed": False
    })
    
    # 处理进度
    processing: Dict[str, Any] = Field(default_factory=lambda: {
        "progress": 0.0,
        "current_step": "",
        "in_progress": False
    })
    
    # UI状态
    ui: Dict[str, Any] = Field(default_factory=lambda: {
        "show_progress": True,
        "show_confirmation": False,
        "show_evaluation": False,
        "active_tab": "chat"
    })


class EssayGenerationRequest(BaseModel):
    """作文生成请求"""
    topic: str = Field(..., description="作文主题")
    requirements: Optional[str] = Field(None, description="具体要求")
    style: Optional[str] = Field("通用", description="写作风格")
    length: Optional[str] = Field("中等", description="文章长度")


class EssayRevisionRequest(BaseModel):
    """作文修改请求"""
    feedback: str = Field(..., description="修改意见")
    original_content: str = Field(..., description="原始内容")
    topic: str = Field(..., description="作文主题")


class EssayEvaluationRequest(BaseModel):
    """作文评分请求"""
    content: str = Field(..., description="作文内容")
    topic: str = Field(..., description="作文主题")
    requirements: Optional[str] = Field(None, description="原始要求")


class EvaluationResult(BaseModel):
    """评分结果"""
    overall_score: float = Field(..., description="总分")
    criteria_scores: Dict[str, float] = Field(..., description="各项评分")
    strengths: List[str] = Field(..., description="优点")
    weaknesses: List[str] = Field(..., description="不足")
    suggestions: List[str] = Field(..., description="改进建议")
    detailed_feedback: str = Field(..., description="详细反馈")


# AG-UI协议事件类型
class EventType:
    RUN_STARTED = "RUN_STARTED"
    RUN_FINISHED = "RUN_FINISHED"
    STATE_SNAPSHOT = "STATE_SNAPSHOT"
    STATE_DELTA = "STATE_DELTA"
    TEXT_MESSAGE_START = "TEXT_MESSAGE_START"
    TEXT_MESSAGE_CONTENT = "TEXT_MESSAGE_CONTENT"
    TEXT_MESSAGE_END = "TEXT_MESSAGE_END"
    TOOL_CALL_START = "TOOL_CALL_START"
    TOOL_CALL_END = "TOOL_CALL_END"


class BaseEvent(BaseModel):
    """基础事件模型"""
    type: str
    timestamp: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat())


class RunStartedEvent(BaseEvent):
    """运行开始事件"""
    type: str = EventType.RUN_STARTED
    thread_id: str
    run_id: str


class RunFinishedEvent(BaseEvent):
    """运行结束事件"""
    type: str = EventType.RUN_FINISHED
    thread_id: str
    run_id: str


class StateSnapshotEvent(BaseEvent):
    """状态快照事件"""
    type: str = EventType.STATE_SNAPSHOT
    message_id: str
    snapshot: Dict[str, Any]


class StateDeltaEvent(BaseEvent):
    """状态增量事件"""
    type: str = EventType.STATE_DELTA
    message_id: str
    delta: List[Dict[str, Any]]


class TextMessageStartEvent(BaseEvent):
    """文本消息开始事件"""
    type: str = EventType.TEXT_MESSAGE_START
    message_id: str
    role: str


class TextMessageContentEvent(BaseEvent):
    """文本消息内容事件"""
    type: str = EventType.TEXT_MESSAGE_CONTENT
    message_id: str
    delta: str


class TextMessageEndEvent(BaseEvent):
    """文本消息结束事件"""
    type: str = EventType.TEXT_MESSAGE_END
    message_id: str


class ToolCallStartEvent(BaseEvent):
    """工具调用开始事件"""
    type: str = EventType.TOOL_CALL_START
    message_id: str
    tool_name: str
    tool_input: Dict[str, Any]


class ToolCallEndEvent(BaseEvent):
    """工具调用结束事件"""
    type: str = EventType.TOOL_CALL_END
    message_id: str
    tool_name: str
    tool_output: Any
