"use client";

import React, { useState, useEffect, useRef } from 'react';
import { PenTool, Send } from 'lucide-react';
import { EssayState, ESSAY_PHASES } from '../../types';
import ProgressBar from './ProgressBar';
import StatusIndicator from './StatusIndicator';
import ConfirmationDialog from './ConfirmationDialog';
import EvaluationDisplay from './EvaluationDisplay';

export default function EssayWriter() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationTimer, setConfirmationTimer] = useState(10);
  const [userInput, setUserInput] = useState('');
  const [essayContent, setEssayContent] = useState('');
  const [currentPhase, setCurrentPhase] = useState<EssayState['status']['phase']>('idle');
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [evaluation, setEvaluation] = useState<any>(null);
  const [messages, setMessages] = useState<Array<{role: string, content: string}>>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 渲染代理状态
  useCoAgentStateRender({
    name: "essayWriterAgent",
    handler: ({ nodeName }) => {
      console.log(`当前节点: ${nodeName}`);
      
      if (nodeName === "__end__") {
        setTimeout(() => {
          setIsProcessing(false);
          stopAgent();
        }, 1000);
      }
    },
    render: ({ status }) => {
      if (status === "inProgress") {
        setIsProcessing(true);
        return (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="animate-spin h-5 w-5 border-2 border-blue-500 rounded-full border-t-transparent"></div>
              <h3 className="font-semibold text-gray-900">
                AI正在处理您的请求...
              </h3>
            </div>

            {/* 状态指示器 */}
            <div className="mb-4">
              <StatusIndicator 
                phase={state?.status?.phase || 'idle'}
                step={state?.processing?.current_step}
              />
            </div>

            {/* 进度条 */}
            {state?.processing?.progress !== undefined && state.processing.progress > 0 && (
              <div className="mb-4">
                <ProgressBar 
                  progress={state.processing.progress}
                  step={state.processing.current_step || "处理中..."}
                />
              </div>
            )}

            {/* 处理信息 */}
            <div className="text-sm text-gray-600">
              {getStatusText()}
            </div>

            {/* 确认对话框 */}
            {state?.status?.phase === 'waiting_confirmation' && (
              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium text-yellow-800">
                    请确认作文内容
                  </span>
                  <span className="text-sm text-yellow-600">
                    {confirmationTimer}秒后自动确认
                  </span>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleConfirm}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    确认并评分
                  </button>
                  <button
                    onClick={() => setShowConfirmation(true)}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    提出修改意见
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      }

      if (status === "complete") {
        setIsProcessing(false);
        return null;
      }

      return null;
    },
  });

  // 获取状态文本
  const getStatusText = () => {
    const phase = state?.status?.phase;
    const step = state?.processing?.current_step;
    
    if (step) return step;
    
    switch (phase) {
      case ESSAY_PHASES.GENERATING:
        return "正在生成作文内容...";
      case ESSAY_PHASES.WAITING_CONFIRMATION:
        return "等待您的确认...";
      case ESSAY_PHASES.REVISING:
        return "正在根据您的意见修改作文...";
      case ESSAY_PHASES.EVALUATING:
        return "正在进行AI评分...";
      case ESSAY_PHASES.COMPLETED:
        return "处理完成！";
      case ESSAY_PHASES.ERROR:
        return `处理出错: ${state?.status?.error || '未知错误'}`;
      default:
        return "准备就绪";
    }
  };

  // 处理确认
  const handleConfirm = () => {
    // 这里可以发送确认消息给代理
    console.log("用户确认作文内容");
    setShowConfirmation(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  // 处理修改
  const handleRevise = (feedback: string) => {
    // 这里可以发送修改意见给代理
    console.log("用户提出修改意见:", feedback);
    setShowConfirmation(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  // 启动确认计时器
  useEffect(() => {
    if (state?.status?.phase === 'waiting_confirmation') {
      setConfirmationTimer(10);
      timerRef.current = setInterval(() => {
        setConfirmationTimer(prev => {
          if (prev <= 1) {
            handleConfirm(); // 自动确认
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [state?.status?.phase]);

  return (
    <div className="space-y-6">
      {/* 当前状态显示 */}
      {!isProcessing && state?.status?.phase === 'idle' && (
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <div className="text-gray-500 mb-4">
            <PenTool className="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p>请在右侧聊天框中输入您想写的作文主题</p>
          </div>
        </div>
      )}

      {/* 评分结果显示 */}
      {state?.evaluation?.completed && (
        <EvaluationDisplay 
          evaluation={{
            overall_score: state.evaluation.score || 0,
            criteria_scores: state.evaluation.criteria,
            strengths: [],
            weaknesses: [],
            suggestions: state.evaluation.suggestions,
            detailed_feedback: ""
          }}
        />
      )}

      {/* 确认对话框 */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        timeoutSeconds={confirmationTimer}
        onConfirm={handleConfirm}
        onRevise={handleRevise}
        onCancel={() => setShowConfirmation(false)}
      />
    </div>
  );
}


