@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动 Essay Writer 系统...

REM 检查环境变量文件
if not exist .env (
    echo ⚠️  未找到 .env 文件，正在创建...
    copy .env.example .env >nul
    echo 📝 请编辑 .env 文件，添加您的 DEEPSEEK_API_KEY
    echo 💡 然后重新运行此脚本
    pause
    exit /b 1
)

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

REM 检查API密钥
findstr "your_deepseek_api_key_here" .env >nul
if not errorlevel 1 (
    echo ❌ 请在 .env 文件中设置您的 DEEPSEEK_API_KEY
    pause
    exit /b 1
)

echo 🔧 构建和启动服务...

REM 构建并启动服务
docker-compose up -d --build

echo ⏳ 等待服务启动...
timeout /t 15 /nobreak >nul

echo 🔍 检查服务状态...

REM 检查后端服务
curl -f http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ 后端服务启动失败
    docker-compose logs backend
    pause
    exit /b 1
) else (
    echo ✅ 后端服务运行正常
)

REM 检查前端服务
curl -f http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo ❌ 前端服务启动失败
    docker-compose logs frontend
    pause
    exit /b 1
) else (
    echo ✅ 前端服务运行正常
)

echo.
echo 🎉 Essay Writer 系统启动成功！
echo.
echo 📱 访问地址：
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:8000
echo    API文档:  http://localhost:8000/docs
echo.
echo 🛠️  管理命令：
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo.
echo 💡 使用提示：
echo    1. 在前端页面的聊天框中输入作文主题
echo    2. AI会实时生成作文内容
echo    3. 10秒内确认或提出修改意见
echo    4. 点击继续获取AI评分
echo.
echo 按任意键打开浏览器...
pause >nul

REM 打开浏览器
start http://localhost:3000
