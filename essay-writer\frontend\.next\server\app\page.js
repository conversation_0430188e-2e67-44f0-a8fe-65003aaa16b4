/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN3b3JrJTVDJTVDZ2l0aHViJTVDJTVDZGFud2VuLWFpJTVDJTVDY2hhdGRiJTVDJTVDZXNzYXktd3JpdGVyJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW9IIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXNzYXktd3JpdGVyLWZyb250ZW5kLz9kZDVkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcd29ya1xcXFxnaXRodWJcXFxcZGFud2VuLWFpXFxcXGNoYXRkYlxcXFxlc3NheS13cml0ZXJcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwork%5C%5Cgithub%5C%5Cdanwen-ai%5C%5Cchatdb%5C%5Cessay-writer%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/components/EssayWriterSimple.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/EssayWriterSimple.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EssayWriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_PenTool_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,PenTool,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_PenTool_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,PenTool,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_PenTool_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,PenTool,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/app/components/ProgressBar.tsx\");\n/* harmony import */ var _StatusIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StatusIndicator */ \"(ssr)/./src/app/components/StatusIndicator.tsx\");\n/* harmony import */ var _EvaluationDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EvaluationDisplay */ \"(ssr)/./src/app/components/EvaluationDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// import ConfirmationDialog from './ConfirmationDialog'; // 不再使用外部对话框\n\nfunction EssayWriter() {\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmation, setShowConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmationTimer, setConfirmationTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [userInput, setUserInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [essayContent, setEssayContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [evaluation, setEvaluation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showRevisionInput, setShowRevisionInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [revisionText, setRevisionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isConfirming, setIsConfirming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRequestInProgress, setIsRequestInProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件卸载时清理计时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (timerRef.current) {\n                clearInterval(timerRef.current);\n            }\n        };\n    }, []);\n    // 处理SSE流式响应\n    const handleStreamResponse = async (response)=>{\n        const reader = response.body?.getReader();\n        const decoder = new TextDecoder();\n        if (!reader) return;\n        try {\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value);\n                const lines = chunk.split(\"\\n\");\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        try {\n                            const data = JSON.parse(line.slice(6));\n                            handleSSEEvent(data);\n                        } catch (e) {\n                            console.error(\"Failed to parse SSE data:\", e);\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Stream reading error:\", error);\n            setIsProcessing(false);\n            setCurrentPhase(\"error\");\n        } finally{\n            reader.releaseLock();\n        }\n    };\n    // 处理SSE事件\n    const handleSSEEvent = (event)=>{\n        console.log(\"SSE Event:\", event);\n        console.log(\"Current Phase:\", currentPhase);\n        switch(event.type){\n            case \"RUN_STARTED\":\n                setIsProcessing(true);\n                setCurrentPhase(\"generating\");\n                setProgress(0.1);\n                setCurrentStep(\"开始生成作文...\");\n                setShowConfirmation(false);\n                setShowRevisionInput(false);\n                setIsConfirming(false);\n                break;\n            case \"STATE_DELTA\":\n                if (event.delta) {\n                    event.delta.forEach((delta)=>{\n                        if (delta.path.includes(\"progress\")) {\n                            setProgress(delta.value);\n                        }\n                        if (delta.path.includes(\"current_step\")) {\n                            setCurrentStep(delta.value);\n                        }\n                        if (delta.path.includes(\"phase\")) {\n                            setCurrentPhase(delta.value);\n                        }\n                    });\n                }\n                break;\n            case \"TEXT_MESSAGE_START\":\n                setEssayContent(\"\");\n                break;\n            case \"TEXT_MESSAGE_CONTENT\":\n                console.log(\"Received text delta:\", event.delta);\n                setEssayContent((prev)=>prev + event.delta);\n                break;\n            case \"TEXT_MESSAGE_END\":\n                console.log(\"TEXT_MESSAGE_END received, currentPhase:\", currentPhase);\n                setCurrentPhase(\"waiting_confirmation\");\n                setShowConfirmation(true);\n                setIsProcessing(false);\n                setProgress(1);\n                setCurrentStep(\"作文生成完成，等待确认...\");\n                startConfirmationTimer();\n                break;\n            case \"RUN_FINISHED\":\n                setIsProcessing(false);\n                setIsConfirming(false);\n                setIsRequestInProgress(false);\n                setCurrentPhase(\"completed\");\n                setProgress(1);\n                setCurrentStep(\"处理完成！\");\n                // 清理倒计时器\n                if (timerRef.current) {\n                    clearInterval(timerRef.current);\n                    timerRef.current = null;\n                }\n                break;\n        }\n    };\n    // 启动确认计时器\n    const startConfirmationTimer = ()=>{\n        console.log(\"Starting confirmation timer...\");\n        // 清理之前的计时器\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n        }\n        setConfirmationTimer(10);\n        timerRef.current = setInterval(()=>{\n            setConfirmationTimer((prev)=>{\n                console.log(\"Timer tick:\", prev);\n                if (prev <= 1) {\n                    // 清理计时器\n                    if (timerRef.current) {\n                        clearInterval(timerRef.current);\n                        timerRef.current = null;\n                    }\n                    console.log(\"Auto-confirming...\");\n                    handleConfirm(); // 自动确认\n                    return 0;\n                }\n                return prev - 1;\n            });\n        }, 1000);\n    };\n    // 发送请求到后端\n    const sendRequest = async (message)=>{\n        if (isRequestInProgress) {\n            console.log(\"Request already in progress, skipping...\");\n            return;\n        }\n        setIsRequestInProgress(true);\n        try {\n            setIsProcessing(true);\n            setCurrentPhase(\"generating\");\n            const requestBody = {\n                thread_id: `thread_${Date.now()}`,\n                run_id: `run_${Date.now()}`,\n                messages: [\n                    {\n                        id: `msg_${Date.now()}`,\n                        role: \"user\",\n                        content: message\n                    }\n                ]\n            };\n            const response = await fetch(\"http://127.0.0.1:8000/api/essay-writer\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            await handleStreamResponse(response);\n        } catch (error) {\n            console.error(\"Request failed:\", error);\n            setIsProcessing(false);\n            setCurrentPhase(\"error\");\n            setCurrentStep(\"请求失败，请重试\");\n        } finally{\n            setIsRequestInProgress(false);\n        }\n    };\n    // 处理用户输入提交\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!userInput.trim() || isProcessing) return;\n        const message = userInput.trim();\n        setUserInput(\"\");\n        // 添加用户消息到聊天记录\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    role: \"user\",\n                    content: message,\n                    timestamp: new Date().toISOString()\n                }\n            ]);\n        await sendRequest(message);\n    };\n    // 处理确认\n    const handleConfirm = ()=>{\n        if (isConfirming) return; // 防止重复调用\n        setIsConfirming(true);\n        setShowConfirmation(false);\n        setShowRevisionInput(false);\n        setCurrentPhase(\"evaluating\");\n        // 清理计时器\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n            timerRef.current = null;\n        }\n        // 添加确认消息到聊天记录\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    role: \"user\",\n                    content: \"确认\",\n                    timestamp: new Date().toISOString()\n                }\n            ]);\n        // 发送确认消息\n        sendRequest(\"确认\");\n    };\n    // 显示修改输入框\n    const handleShowRevisionInput = ()=>{\n        setShowRevisionInput(true);\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n        }\n    };\n    // 处理修改提交\n    const handleRevisionSubmit = ()=>{\n        if (!revisionText.trim()) return;\n        setShowConfirmation(false);\n        setShowRevisionInput(false);\n        setCurrentPhase(\"revising\");\n        // 添加修改意见到聊天记录\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    role: \"user\",\n                    content: revisionText,\n                    timestamp: new Date().toISOString()\n                }\n            ]);\n        // 发送修改意见\n        sendRequest(revisionText);\n        setRevisionText(\"\");\n    };\n    // 取消修改\n    const handleCancelRevision = ()=>{\n        setShowRevisionInput(false);\n        setRevisionText(\"\");\n        // 重新启动倒计时\n        startConfirmationTimer();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                        children: \"\\uD83D\\uDCAC 作文生成对话\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 mb-6 max-h-96 overflow-y-auto\",\n                        children: [\n                            messages.length === 0 && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_PenTool_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"请输入您想写的作文主题开始生成\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `message-bubble ${message.role === \"user\" ? \"user-message\" : \"assistant-message\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium mb-1\",\n                                            children: message.role === \"user\" ? \"您\" : \"AI助手\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)),\n                            essayContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"assistant-message\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium mb-1\",\n                                        children: \"AI助手\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"essay-content whitespace-pre-wrap\",\n                                        children: [\n                                            essayContent,\n                                            isProcessing && currentPhase === \"generating\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cursor-blink\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_PenTool_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 animate-spin text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatusIndicator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        phase: currentPhase,\n                                        step: currentStep\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                progress: progress,\n                                step: currentStep\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-2 bg-gray-100 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Phase: \",\n                                    currentPhase\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Show Confirmation: \",\n                                    showConfirmation.toString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Show Revision: \",\n                                    showRevisionInput.toString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Is Processing: \",\n                                    isProcessing.toString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Is Confirming: \",\n                                    isConfirming.toString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Is Request In Progress: \",\n                                    isRequestInProgress.toString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    showConfirmation && !showRevisionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"✅ 作文生成完成\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"自动确认倒计时: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-bold text-red-600\",\n                                                children: [\n                                                    confirmationTimer,\n                                                    \"s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 26\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 mb-4\",\n                                children: \"请选择下一步操作：\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleConfirm,\n                                        className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2\",\n                                        children: \"✓ 确认并评分\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShowRevisionInput,\n                                        className: \"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2\",\n                                        children: \"✏️ 修改作文\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    showRevisionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900 mb-3\",\n                                children: \"请输入修改意见\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: revisionText,\n                                onChange: (e)=>setRevisionText(e.target.value),\n                                placeholder: \"请描述您希望如何修改这篇作文，例如：增加更多具体例子、调整文章结构、改变写作风格等...\",\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                rows: 3\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRevisionSubmit,\n                                        disabled: !revisionText.trim(),\n                                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                        children: \"提交修改意见\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancelRevision,\n                                        className: \"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this),\n                    !showConfirmation && !showRevisionInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: userInput,\n                                onChange: (e)=>setUserInput(e.target.value),\n                                placeholder: \"请输入作文主题或要求，例如：写一篇关于友谊的作文\",\n                                className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                disabled: isProcessing\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !userInput.trim() || isProcessing,\n                                className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_PenTool_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"发送\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            evaluation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EvaluationDisplay__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                evaluation: evaluation\n            }, void 0, false, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EssayWriterSimple.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/EssayWriterSimple.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/EvaluationDisplay.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/EvaluationDisplay.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EvaluationDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,BarChart3,Lightbulb,Star,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,BarChart3,Lightbulb,Star,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,BarChart3,Lightbulb,Star,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,BarChart3,Lightbulb,Star,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,BarChart3,Lightbulb,Star,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,BarChart3,Lightbulb,Star,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction EvaluationDisplay({ evaluation, className = \"\" }) {\n    const getScoreColor = (score)=>{\n        if (score >= 90) return \"text-green-600\";\n        if (score >= 80) return \"text-blue-600\";\n        if (score >= 70) return \"text-yellow-600\";\n        if (score >= 60) return \"text-orange-600\";\n        return \"text-red-600\";\n    };\n    const getScoreGrade = (score)=>{\n        if (score >= 90) return \"优秀\";\n        if (score >= 80) return \"良好\";\n        if (score >= 70) return \"中等\";\n        if (score >= 60) return \"及格\";\n        return \"需要改进\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg shadow-sm border p-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 bg-purple-100 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-6 h-6 text-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"\\uD83D\\uDCCA AI评分结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"基于多维度评价标准的智能评分\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-8 h-8 text-yellow-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-medium text-gray-700\",\n                                children: \"总分\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `evaluation-score ${getScoreColor(evaluation.overall_score)}`,\n                        children: evaluation.overall_score.toFixed(1)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium text-gray-600 mt-1\",\n                        children: getScoreGrade(evaluation.overall_score)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 text-yellow-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            \"各项评分\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"evaluation-criteria\",\n                        children: Object.entries(evaluation.criteria_scores).map(([criterion, score])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"criteria-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700\",\n                                        children: criterion\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: `${score / 25 * 100}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `font-semibold ${getScoreColor(score)}`,\n                                                children: score.toFixed(1)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, criterion, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            evaluation.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 text-green-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            \"✅ 优点\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2\",\n                        children: evaluation.strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-2 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-500 mt-1\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: strength\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            evaluation.weaknesses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5 text-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            \"⚠️ 需要改进\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2\",\n                        children: evaluation.weaknesses.map((weakness, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-2 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-500 mt-1\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: weakness\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this),\n            evaluation.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_BarChart3_Lightbulb_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            \"\\uD83D\\uDCA1 改进建议\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2\",\n                        children: evaluation.suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-2 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-500 mt-1\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: suggestion\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            evaluation.detailed_feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                        children: \"\\uD83D\\uDCDD 详细反馈\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-sm max-w-none text-gray-700\",\n                        children: evaluation.detailed_feedback.split(\"\\n\").map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2\",\n                                children: paragraph\n                            }, index, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-3 mt-6 pt-6 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.print(),\n                        className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                        children: \"打印报告\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            // 这里可以添加重新生成的逻辑\n                            window.location.reload();\n                        },\n                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"重新生成\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\EvaluationDisplay.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/EvaluationDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/Header.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Header.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PenTool,RefreshCw,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PenTool,RefreshCw,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PenTool,RefreshCw,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PenTool,RefreshCw,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Essay Writer\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"AI智能作文生成系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"智能生成\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"实时修改\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PenTool_RefreshCw_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"AI评分\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"v1.0.0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"AG-UI + LangGraph\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\Header.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/ProgressBar.tsx":
/*!********************************************!*\
  !*** ./src/app/components/ProgressBar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProgressBar({ progress, step, className = \"\" }) {\n    const percentage = Math.min(Math.max(progress * 100, 0), 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-2 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-700 font-medium\",\n                        children: step || \"处理中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: [\n                            percentage.toFixed(0),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"progress-bar\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"progress-fill\",\n                    style: {\n                        width: `${percentage}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-xs text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: percentage >= 10 ? \"text-blue-600\" : \"\",\n                        children: \"开始\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: percentage >= 30 ? \"text-blue-600\" : \"\",\n                        children: \"生成中\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: percentage >= 70 ? \"text-blue-600\" : \"\",\n                        children: \"完善中\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: percentage >= 90 ? \"text-blue-600\" : \"\",\n                        children: \"等待确认\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: percentage >= 100 ? \"text-green-600\" : \"\",\n                        children: \"完成\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\ProgressBar.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/StatusIndicator.tsx":
/*!************************************************!*\
  !*** ./src/app/components/StatusIndicator.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatusIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Edit3,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types */ \"(ssr)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction StatusIndicator({ phase, step, className = \"\" }) {\n    const getStatusConfig = ()=>{\n        switch(phase){\n            case _types__WEBPACK_IMPORTED_MODULE_2__.ESSAY_PHASES.GENERATING:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 17\n                    }, this),\n                    text: \"正在生成作文\",\n                    className: \"status-generating\"\n                };\n            case _types__WEBPACK_IMPORTED_MODULE_2__.ESSAY_PHASES.WAITING_CONFIRMATION:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 17\n                    }, this),\n                    text: \"等待确认\",\n                    className: \"status-waiting\"\n                };\n            case _types__WEBPACK_IMPORTED_MODULE_2__.ESSAY_PHASES.REVISING:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 17\n                    }, this),\n                    text: \"正在修改\",\n                    className: \"status-revising\"\n                };\n            case _types__WEBPACK_IMPORTED_MODULE_2__.ESSAY_PHASES.EVALUATING:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 17\n                    }, this),\n                    text: \"正在评分\",\n                    className: \"status-evaluating\"\n                };\n            case _types__WEBPACK_IMPORTED_MODULE_2__.ESSAY_PHASES.COMPLETED:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 17\n                    }, this),\n                    text: \"已完成\",\n                    className: \"status-completed\"\n                };\n            case _types__WEBPACK_IMPORTED_MODULE_2__.ESSAY_PHASES.ERROR:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 17\n                    }, this),\n                    text: \"处理出错\",\n                    className: \"status-error\"\n                };\n            default:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Edit3_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-4 h-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 17\n                    }, this),\n                    text: \"准备中\",\n                    className: \"status-indicator\"\n                };\n        }\n    };\n    const config = getStatusConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `status-indicator ${config.className} ${className}`,\n        children: [\n            config.icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-medium\",\n                children: config.text\n            }, void 0, false, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            step && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs opacity-75\",\n                children: [\n                    \"- \",\n                    step\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\components\\\\StatusIndicator.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/StatusIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_EssayWriterSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/EssayWriterSimple */ \"(ssr)/./src/app/components/EssayWriterSimple.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Header */ \"(ssr)/./src/app/components/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"\\uD83E\\uDD16 AI作文生成系统\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 mb-6\",\n                                    children: \"基于AG-UI协议和LangGraph的智能作文生成系统\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: \"✨ 智能生成\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: \"\\uD83D\\uDD04 实时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: \"\\uD83D\\uDCCA AI评分\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: \"⚡ 流式输出\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border p-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"\\uD83D\\uDCDD 使用说明\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"操作流程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal list-inside space-y-1 text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"在下方输入框中输入作文主题或要求\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"AI实时生成作文内容\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"10秒内确认或提出修改意见\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 49,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '点击\"继续\"获取AI评分'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"功能特色\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"支持多轮修改（最多3次）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"实时流式输出，体验流畅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 57,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"专业AI评分和改进建议\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"自动确认机制，操作便捷\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EssayWriterSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQytCO0FBQ2hCO0FBRTFCLFNBQVNHO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0gsMERBQU1BOzs7OzswQkFFUCw4REFBQ0k7Z0JBQUtELFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBR0YsV0FBVTs4Q0FBd0M7Ozs7Ozs4Q0FHdEQsOERBQUNHO29DQUFFSCxXQUFVOzhDQUE2Qjs7Ozs7OzhDQUcxQyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FBS0osV0FBVTtzREFBMEI7Ozs7OztzREFHMUMsOERBQUNJOzRDQUFLSixXQUFVO3NEQUEwQjs7Ozs7O3NEQUcxQyw4REFBQ0k7NENBQUtKLFdBQVU7c0RBQTBCOzs7Ozs7c0RBRzFDLDhEQUFDSTs0Q0FBS0osV0FBVTtzREFBMEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPOUMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0s7b0NBQUdMLFdBQVU7OENBQTJDOzs7Ozs7OENBR3pELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzs4REFDQyw4REFBQ087b0RBQUdOLFdBQVU7OERBQWlDOzs7Ozs7OERBQy9DLDhEQUFDTztvREFBR1AsV0FBVTs7c0VBQ1osOERBQUNRO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBR1IsOERBQUNUOzs4REFDQyw4REFBQ087b0RBQUdOLFdBQVU7OERBQWlDOzs7Ozs7OERBQy9DLDhEQUFDUztvREFBR1QsV0FBVTs7c0VBQ1osOERBQUNRO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT1osOERBQUNaLHFFQUFXQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUt0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Vzc2F5LXdyaXRlci1mcm9udGVuZC8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFc3NheVdyaXRlciBmcm9tICcuL2NvbXBvbmVudHMvRXNzYXlXcml0ZXJTaW1wbGUnO1xuaW1wb3J0IEhlYWRlciBmcm9tICcuL2NvbXBvbmVudHMvSGVhZGVyJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby0xMDBcIj5cbiAgICAgIDxIZWFkZXIgLz5cblxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICB7Lyog5qyi6L+O5Yy65Z+fICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItOFwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAg8J+kliBBSeS9nOaWh+eUn+aIkOezu+e7n1xuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgICAgIOWfuuS6jkFHLVVJ5Y2P6K6u5ZKMTGFuZ0dyYXBo55qE5pm66IO95L2c5paH55Sf5oiQ57O757ufXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGp1c3RpZnktY2VudGVyIGdhcC00IHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgIOKcqCDmmbrog73nlJ/miJBcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgIPCflIQg5a6e5pe25L+u5pS5XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICDwn5OKIEFJ6K+E5YiGXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICDimqEg5rWB5byP6L6T5Ye6XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOS9v+eUqOivtOaYjiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlciBwLTYgbWItOFwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAg8J+TnSDkvb/nlKjor7TmmI5cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+5pON5L2c5rWB56iLPC9oMz5cbiAgICAgICAgICAgICAgICA8b2wgY2xhc3NOYW1lPVwibGlzdC1kZWNpbWFsIGxpc3QtaW5zaWRlIHNwYWNlLXktMSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICA8bGk+5Zyo5LiL5pa56L6T5YWl5qGG5Lit6L6T5YWl5L2c5paH5Li76aKY5oiW6KaB5rGCPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT5BSeWunuaXtueUn+aIkOS9nOaWh+WGheWuuTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+MTDnp5LlhoXnoa7orqTmiJbmj5Dlh7rkv67mlLnmhI/op4E8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPueCueWHu1wi57un57utXCLojrflj5ZBSeivhOWIhjwvbGk+XG4gICAgICAgICAgICAgICAgPC9vbD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPuWKn+iDveeJueiJsjwvaDM+XG4gICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtZGlzYyBsaXN0LWluc2lkZSBzcGFjZS15LTEgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgPGxpPuaUr+aMgeWkmui9ruS/ruaUue+8iOacgOWkmjPmrKHvvIk8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPuWunuaXtua1geW8j+i+k+WHuu+8jOS9k+mqjOa1geeVhTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+5LiT5LiaQUnor4TliIblkozmlLnov5vlu7rorq48L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPuiHquWKqOehruiupOacuuWItu+8jOaTjeS9nOS+v+aNtzwvbGk+XG4gICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDkuLvopoHnu4Tku7YgKi99XG4gICAgICAgICAgPEVzc2F5V3JpdGVyIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiRXNzYXlXcml0ZXIiLCJIZWFkZXIiLCJIb21lUGFnZSIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iLCJoMSIsInAiLCJzcGFuIiwiaDIiLCJoMyIsIm9sIiwibGkiLCJ1bCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TIMEOUT_SECONDS: () => (/* binding */ DEFAULT_TIMEOUT_SECONDS),\n/* harmony export */   ESSAY_PHASES: () => (/* binding */ ESSAY_PHASES),\n/* harmony export */   MAX_FEEDBACK_LENGTH: () => (/* binding */ MAX_FEEDBACK_LENGTH),\n/* harmony export */   MAX_REVISION_ROUNDS: () => (/* binding */ MAX_REVISION_ROUNDS),\n/* harmony export */   MAX_TOPIC_LENGTH: () => (/* binding */ MAX_TOPIC_LENGTH),\n/* harmony export */   MIN_TOPIC_LENGTH: () => (/* binding */ MIN_TOPIC_LENGTH)\n/* harmony export */ });\n/**\n * TypeScript类型定义\n */ // 作文状态接口\n// 常量\nconst ESSAY_PHASES = {\n    IDLE: \"idle\",\n    GENERATING: \"generating\",\n    WAITING_CONFIRMATION: \"waiting_confirmation\",\n    REVISING: \"revising\",\n    EVALUATING: \"evaluating\",\n    COMPLETED: \"completed\",\n    ERROR: \"error\"\n};\nconst DEFAULT_TIMEOUT_SECONDS = 10;\nconst MAX_REVISION_ROUNDS = 3;\nconst MIN_TOPIC_LENGTH = 2;\nconst MAX_TOPIC_LENGTH = 200;\nconst MAX_FEEDBACK_LENGTH = 500;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cc623f51569f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXNzYXktd3JpdGVyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8yYmJhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2M2MjNmNTE1NjlmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Essay Writer - AI作文生成系统\",\n    description: \"基于AG-UI协议和LangGraph的智能作文生成系统，支持实时流式输出、用户反馈和AI评分\",\n    keywords: [\n        \"AI作文\",\n        \"智能写作\",\n        \"LangGraph\",\n        \"AG-UI\",\n        \"作文评分\"\n    ],\n    authors: [\n        {\n            name: \"Essay Writer Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\work\\\\github\\\\danwen-ai\\\\chatdb\\\\essay-writer\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFRO1FBQVE7UUFBYTtRQUFTO0tBQU87SUFDeERDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQW9CO0tBQUU7SUFDeENDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdiLCtKQUFlO3NCQUM5Qiw0RUFBQ2M7Z0JBQUlELFdBQVU7MEJBQ1pKOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lc3NheS13cml0ZXItZnJvbnRlbmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0Vzc2F5IFdyaXRlciAtIEFJ5L2c5paH55Sf5oiQ57O757ufJyxcbiAgZGVzY3JpcHRpb246ICfln7rkuo5BRy1VSeWNj+iuruWSjExhbmdHcmFwaOeahOaZuuiDveS9nOaWh+eUn+aIkOezu+e7n++8jOaUr+aMgeWunuaXtua1geW8j+i+k+WHuuOAgeeUqOaIt+WPjemmiOWSjEFJ6K+E5YiGJyxcbiAga2V5d29yZHM6IFsnQUnkvZzmlocnLCAn5pm66IO95YaZ5L2cJywgJ0xhbmdHcmFwaCcsICdBRy1VSScsICfkvZzmlofor4TliIYnXSxcbiAgYXV0aG9yczogW3sgbmFtZTogJ0Vzc2F5IFdyaXRlciBUZWFtJyB9XSxcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJ2aWV3cG9ydCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\work\github\danwen-ai\chatdb\essay-writer\frontend\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\work\github\danwen-ai\chatdb\essay-writer\frontend\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwork%5Cgithub%5Cdanwen-ai%5Cchatdb%5Cessay-writer%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();