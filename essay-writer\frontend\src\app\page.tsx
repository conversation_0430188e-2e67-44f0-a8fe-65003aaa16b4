"use client";

import React from 'react';
import EssayWriter from './components/EssayWriter';
import Header from './components/Header';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 欢迎区域 */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🤖 AI作文生成系统
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              基于AG-UI协议和LangGraph的智能作文生成系统
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <span className="flex items-center gap-1">
                ✨ 智能生成
              </span>
              <span className="flex items-center gap-1">
                🔄 实时修改
              </span>
              <span className="flex items-center gap-1">
                📊 AI评分
              </span>
              <span className="flex items-center gap-1">
                ⚡ 流式输出
              </span>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              📝 使用说明
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">操作流程</h3>
                <ol className="list-decimal list-inside space-y-1 text-gray-600">
                  <li>在下方输入框中输入作文主题或要求</li>
                  <li>AI实时生成作文内容</li>
                  <li>10秒内确认或提出修改意见</li>
                  <li>点击"继续"获取AI评分</li>
                </ol>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">功能特色</h3>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>支持多轮修改（最多3次）</li>
                  <li>实时流式输出，体验流畅</li>
                  <li>专业AI评分和改进建议</li>
                  <li>自动确认机制，操作便捷</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 主要组件 */}
          <EssayWriter />
        </div>
      </main>
    </div>
  );
}
