"use client";

import React from 'react';
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";
import EssayWriter from './components/EssayWriter';
import Header from './components/Header';

const runtimeUrl = process.env.NEXT_PUBLIC_COPILOTKIT_RUNTIME_URL || '/api/copilotkit';

export default function HomePage() {
  return (
    <CopilotKit
      runtimeUrl={runtimeUrl}
      agent="essayWriterAgent"
      showDevConsole={false}
    >
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Header />
        
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 欢迎区域 */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                🤖 AI作文生成系统
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                基于AG-UI协议和LangGraph的智能作文生成系统
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  ✨ 智能生成
                </span>
                <span className="flex items-center gap-1">
                  🔄 实时修改
                </span>
                <span className="flex items-center gap-1">
                  📊 AI评分
                </span>
                <span className="flex items-center gap-1">
                  ⚡ 流式输出
                </span>
              </div>
            </div>

            {/* 使用说明 */}
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                📝 使用说明
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">操作流程</h3>
                  <ol className="list-decimal list-inside space-y-1 text-gray-600">
                    <li>在聊天框中输入作文主题或要求</li>
                    <li>AI实时生成作文内容</li>
                    <li>10秒内确认或提出修改意见</li>
                    <li>点击"继续"获取AI评分</li>
                  </ol>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">功能特色</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    <li>支持多轮修改（最多3次）</li>
                    <li>实时流式输出，体验流畅</li>
                    <li>专业AI评分和改进建议</li>
                    <li>自动确认机制，操作便捷</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 示例主题 */}
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                💡 示例主题
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  "我的理想",
                  "环保的重要性",
                  "科技改变生活",
                  "友谊的力量",
                  "读书的乐趣",
                  "家乡的变化"
                ].map((topic, index) => (
                  <button
                    key={index}
                    className="p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border transition-colors"
                    onClick={() => {
                      // 这里可以添加点击示例主题的逻辑
                      console.log(`选择主题: ${topic}`);
                    }}
                  >
                    <span className="text-gray-700">{topic}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* 主要组件 */}
            <EssayWriter />
          </div>
        </main>

        {/* CopilotKit侧边栏 */}
        <CopilotSidebar
          clickOutsideToClose={true}
          defaultOpen={false}
          labels={{
            title: "AI作文助手",
            initial: "您好！我是AI作文助手，请告诉我您想写什么主题的作文？",
          }}
          className="w-96"
        />
      </div>
    </CopilotKit>
  );
}
